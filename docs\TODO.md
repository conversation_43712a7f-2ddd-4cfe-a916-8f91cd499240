# Future Projects and Enhancements

## 1. Strategy Simulator / Backtester

**Objective:** Build a tool to backtest alternative exit strategies against the historical trade data.

**Key Features:**
- Load historical trade data.
- Simulate trades with a user-defined exit strategy (e.g., a fixed R-Multiple target like 3R).
- Calculate and display the simulated performance statistics (win rate, expectancy, etc.).
- Compare the simulated results to the actual historical performance.
