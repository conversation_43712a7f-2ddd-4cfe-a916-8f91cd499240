#!/usr/bin/python
# -*- coding: utf-8 -*-
import pandas as pd
import re
import os
from collections import deque

def generate_trades_fifo(df, symbol):
    """
    Generates trades for a given symbol using FIFO logic.
    Returns both closed trades and open positions.
    """
    transactions = df[df['Symbol'] == symbol].copy()
    transactions['Date'] = pd.to_datetime(transactions['Date'])
    transactions = transactions.sort_values(by='Date')

    buys = deque()
    sells = deque()

    for _, row in transactions.iterrows():
        if row['Quantity'] > 0:
            buys.append(row.to_dict())
        else:
            sells.append(row.to_dict())

    trades = []
    while buys and sells:
        buy = buys[0]
        sell = sells[0]

        trade_quantity = min(buy['Quantity'], abs(sell['Quantity']))

        if trade_quantity > 0:
            buy_price = float(buy['Price'])
            sell_price = float(sell['Price'])
            profit = (sell_price - buy_price) * trade_quantity

            # --- R-Multiple Calculation ---
            initial_risk_per_share = buy_price * 0.08 # 8% max stop loss
            initial_total_risk = initial_risk_per_share * trade_quantity
            r_multiple = profit / initial_total_risk if initial_total_risk != 0 else 0

            trades.append({
                'Symbol': symbol,
                'BuyDate': buy['Date'],
                'SellDate': sell['Date'],
                'Quantity': trade_quantity,
                'BuyPrice': buy_price,
                'SellPrice': sell_price,
                'P/L': profit,
                '$Transaction': buy_price * trade_quantity,
                'R-Multiple': r_multiple
            })

            buy['Quantity'] -= trade_quantity
            sell['Quantity'] += trade_quantity

        if buy['Quantity'] == 0:
            buys.popleft()
        if sell['Quantity'] == 0:
            sells.popleft()

    open_positions = list(buys) + list(sells)
    return trades, open_positions

def load_and_clean_data(activity_csv, stock_pattern):
    """
    Loads and cleans the activity data from the CSV file.
    """
    df = pd.read_csv(activity_csv, engine='python', skiprows=6)
    df = df.dropna()
    df = df.replace({'\$': '', ',': ''}, regex=True)

    # Ensure 'Quantity' is treated as a string before trying to replace commas
    df['Quantity'] = df['Quantity'].astype(str).str.replace(',', '')
    df['Quantity'] = pd.to_numeric(df['Quantity'], errors='coerce').fillna(0).astype(int)

    # Filter for stocks
    df = df[df['Symbol'].apply(lambda x: bool(re.match(stock_pattern, str(x))))]

    # Convert 'Price' and 'Amount' to numeric, coercing errors
    for col in ['Price', 'Amount']:
        df[col] = pd.to_numeric(df[col], errors='coerce')

    return df.dropna(subset=['Price', 'Amount'])

def highlight_col(df):
    """
    Highlights rows based on the distribution of P/L.
    """
    style_df = pd.DataFrame('', index=df.index, columns=df.columns)
    pl = df['P/L']

    # Define conditions based on quantiles
    if not pl.empty:
        # Separate profits and losses to calculate quantiles independently
        profits = pl[pl > 0]
        losses = pl[pl < 0]

        # Define thresholds, handling cases with no profits or no losses
        p25 = profits.quantile(0.25) if not profits.empty else 0
        p75 = profits.quantile(0.75) if not profits.empty else 0
        l25 = losses.quantile(0.25) if not losses.empty else 0
        l75 = losses.quantile(0.75) if not losses.empty else 0

        # Conditions for styling
        green_cond = pl >= p75
        red_cond = pl <= l25
        orange_profit_cond = (pl > 0) & (pl < p75)
        orange_loss_cond = (pl < 0) & (pl > l25)

        # Apply styles
        style_df.loc[green_cond, :] = 'background-color: green'
        style_df.loc[red_cond, :] = 'background-color: red'
        style_df.loc[orange_profit_cond, :] = 'background-color: lightgreen' # A lighter green for smaller profits
        style_df.loc[orange_loss_cond, :] = 'background-color: orange'

    return style_df

def main():
    """
    Main function to run the trade analysis.
    """
    stock_pattern = r'^[A-Z]{1,5}$'

    data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'trade_analysis')
    os.makedirs(data_dir, exist_ok=True)

    activity_csv = r'C:\Users\<USER>\Desktop\pythonicfin\data\trade_analysis\activity.csv'

    df = load_and_clean_data(activity_csv, stock_pattern)

    all_trades = []
    all_open_positions = []
    symbols = df['Symbol'].unique()

    print("Processing trades for each symbol...")
    for symbol in symbols:
        trades, open_positions = generate_trades_fifo(df, symbol)
        all_trades.extend(trades)
        all_open_positions.extend(open_positions)
        if trades:
            print(f"  - {symbol}: Found {len(trades)} closed trades.")
        if open_positions:
            print(f"  - {symbol}: Found {len(open_positions)} open positions.")


    if not all_trades:
        print("\nNo closed trades were generated. Check the input file and stock pattern.")

    if all_open_positions:
        print("\n--- Summary of Open Positions ---")
        open_df = pd.DataFrame(all_open_positions)
        print(open_df[['Symbol', 'Date', 'Quantity', 'Price']])
        print("---------------------------------")


    if not all_trades:
        return

    op = pd.DataFrame(all_trades)
    op.sort_values(by='BuyDate', ascending=False, inplace=True)
    op.reset_index(drop=True, inplace=True)

    output_excel_path = os.path.join(data_dir, 'trades.xlsx')
    try:
        styled_df = op.style.apply(highlight_col, axis=None)
        styled_df.to_excel(output_excel_path, engine='openpyxl', index=False)
        print(f"\nSuccessfully generated trades report at: {output_excel_path}")
    except Exception as e:
        print(f"\nError exporting to Excel: {e}")
        # Fallback to CSV if styling fails
        op.to_csv(os.path.join(data_dir, 'trades.csv'), index=False)
        print("Saved as CSV due to Excel export error.")

if __name__ == "__main__":
    main()
