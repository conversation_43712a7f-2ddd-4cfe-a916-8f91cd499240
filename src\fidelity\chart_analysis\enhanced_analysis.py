# enhanced_analysis.py - Advanced technical analysis with additional metrics and filtering
#
# This module extends the basic technical analysis with more sophisticated filtering options
# focused on fundamental metrics like EPS growth, revenue growth, and RS (Relative Strength) ratings.
# It's designed to identify high-growth stocks with strong financial performance and momentum.
#
# Usage:
#   1. Import the module: from fidelity.chart_analysis.enhanced_analysis import groupAndFilter
#   2. Load your stock data into a pandas DataFrame with required columns
#   3. Use the groupAndFilter() function with filter='Y' to apply fundamental filters
#   4. Optionally specify rs_rating_filter (1-99) to filter by minimum RS rating
#
# Required DataFrame columns:
#   - Symbol: Stock ticker symbol
#   - Sector: Stock's sector classification
#   - Industry: Stock's industry classification
#   - Security Type: Type of security (e.g., 'Common Stock', 'Common Stock (REIT)')
#   - EPS Growth (TTM vs Prior TTM): Trailing twelve months EPS growth
#   - EPS Growth (Last Qtr vs. Same Qtr Prior Yr): Quarter-over-quarter EPS growth
#   - Revenue Growth (TTM vs. Prior TTM): Trailing twelve months revenue growth
#   - Revenue Growth (Last Qtr vs. Same Qtr Prior Yr): Quarter-over-quarter revenue growth
#   - EPS Growth (Proj Next Yr vs. This Yr): Projected EPS growth
#
# RS Rating Filter:
#   - RS ratings range from 1-99, with 99 being the strongest relative performance
#   - Stocks with RS ratings above 70 are generally considered strong performers
#   - The filter uses pre-calculated RS ratings from src/data/rs_data/rs_stocks.csv
#   - Stocks not found in the RS data file are assigned a rating of 0

import pandas as pd
import asyncio
import glob
from fidelity.chart_analysis.chart_downloader import downloadChartsFidelityV2 # Updated import to new file name

def printFilteredData(tickers):
    """
    Filter and print stock symbols from a list, keeping only valid ticker symbols.

    This function filters a list to keep only string entries with 5 or fewer characters,
    which is the standard format for most stock ticker symbols.

    Args:
        tickers (list): List of potential stock symbols to filter

    Returns:
        None: Results are printed to console
    """
    # Filter to keep only string entries with 5 or fewer characters (valid ticker format)
    stock_symbols = [entry for entry in tickers if isinstance(entry, str) and len(entry) <= 5]

    # Print the filtered list of symbols
    print(stock_symbols)

    # Print the count of symbols
    print(len(stock_symbols))

def groupAndFilter(df, filter=None, rs_rating_filter=None):
    """
    Group and filter stocks by sector with advanced fundamental filters.

    This function organizes stocks by sector and applies various fundamental filters
    based on EPS growth and revenue growth metrics. It's designed to identify high-growth
    stocks with strong financial performance.

    Args:
        df (pd.DataFrame): DataFrame containing stock data with required columns
        filter (str, optional): If 'Y', applies fundamental growth filters
        rs_rating_filter (int, optional): Minimum RS rating threshold (1-99)

    Returns:
        pd.DataFrame: Filtered and processed DataFrame
    """
    # Remove REITs from the analysis
    df = df[df['Security Type'] != 'Common Stock (REIT)']

    # Count stocks in each sector to identify dominant sectors
    sector_counts = df['Sector'].value_counts().reset_index(name='Sector_Count')
    sector_counts.columns = ['Sector', 'Sector_Count']

    # Add sector counts to the main dataframe
    df = pd.merge(df, sector_counts, on='Sector')

    # Apply fundamental filters if requested
    if filter == 'Y':
        # Filter 1: Companies with strong EPS growth (15%+)
        df_filtered = df[(
            ((df['EPS Growth (TTM vs Prior TTM)'] >= 15) |
             (df['EPS Growth (Last Qtr vs. Same Qtr Prior Yr)'] >= 15))
        )]

        # Filter 2: Pre-earnings growth companies with strong revenue growth (30%+)
        # These may not have positive EPS yet but show strong revenue momentum
        df_filtered1 = df[(
            ((df['EPS Growth (TTM vs Prior TTM)'].isna()) |
              (df['EPS Growth (Last Qtr vs. Same Qtr Prior Yr)'].isna())) &
            ((df['Revenue Growth (TTM vs. Prior TTM)'] >= 30) |
              (df['Revenue Growth (Last Qtr vs. Same Qtr Prior Yr)'] >= 30))
        )]

        # Filter 3: Companies with exceptional projected growth (50%+)
        df_filtered2 = df[(df['EPS Growth (Proj Next Yr vs. This Yr)'] > 50)]

        # Combine all filtered results
        df = pd.concat([df_filtered, df_filtered1, df_filtered2], ignore_index=True)


    # Apply additional technical filters

    # Calculate 52-week low price based on current price and % above low
    df["52_Week_Low"] = df["Security Price"] * (1 - df["% Above 52 Week Low"] / 100)

    # Filter for stocks that are at least 50% above their 52-week low
    # This helps identify stocks in established uptrends
    df = df[df["Security Price"] >= df["52_Week_Low"] * 1.5]

    # Add MOMO column: True if at least 50% above 52-week low, else False
    df["MOMO"] = df["Security Price"] >= df["52_Week_Low"] * 1.5

    # Filter for stocks that are at least 50% above their 52-week low
    # This helps identify stocks in established uptrends
    df = df[df["MOMO"]]

    # Remove any duplicate symbols that might have been introduced
    df = df.drop_duplicates(subset='Symbol')

    # Apply RS rating filter if specified
    if rs_rating_filter is not None and rs_rating_filter > 0:
        print(f"Loading RS ratings from file and filtering for RS >= {rs_rating_filter}...")

        # Load RS ratings from the rs_stocks.csv file
        import os
        rs_stocks_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'rs_data', 'rs_stocks.csv')

        try:
            rs_df = pd.read_csv(rs_stocks_path)
            # Use the Percentile column as the RS rating and rename Ticker to Symbol for merging
            rs_df = rs_df[['Ticker', 'Percentile']].rename(columns={'Ticker': 'Symbol', 'Percentile': 'RS_Rating'})

            # Merge RS ratings with the main dataframe
            df = pd.merge(df, rs_df, on='Symbol', how='left')

            # Fill missing RS ratings with 0 (stocks not found in RS data)
            df['RS_Rating'] = df['RS_Rating'].fillna(0)

            # Filter by RS rating threshold
            initial_count = len(df)
            df = df[df['RS_Rating'] >= rs_rating_filter]
            final_count = len(df)

            print(f"Filtered from {initial_count} to {final_count} stocks with RS Rating >= {rs_rating_filter}")

            if final_count > 0:
                print("Sample of stocks with their RS ratings:")
                sample_df = df[['Symbol', 'RS_Rating']].head(10)
                for _, row in sample_df.iterrows():
                    print(f"  {row['Symbol']}: RS Rating = {row['RS_Rating']}")

        except FileNotFoundError:
            print(f"RS stocks file not found at {rs_stocks_path}. Skipping RS filter.")
        except Exception as e:
            print(f"Error loading RS ratings: {e}. Skipping RS filter.")

    return df



def chartAnalysis():
    """
    Perform enhanced chart analysis on stocks from screener results files.

    This function:
    1. Loads stock data from all Excel files matching 'screener_results*.xls'
    2. Combines the data and removes duplicates
    3. Applies fundamental and technical filters
    4. Downloads charts for the filtered stocks
    """
    # Use glob to find all files that start with "screener_results"
    import os
    # Try multiple possible locations for screener results files
    screener_dir_1 = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'screener')
    screener_dir_2 = 'src/data/screener'
    screener_dir_3 = '.'

    # Check which directory exists and contains the files
    if os.path.exists(screener_dir_1):
        file_list = glob.glob(os.path.join(screener_dir_1, "screener_results*.xls"))
    elif os.path.exists(screener_dir_2):
        file_list = glob.glob(os.path.join(screener_dir_2, "screener_results*.xls"))
    else:
        file_list = glob.glob("screener_results*.xls")

    print(f"Looking for screener results in: {screener_dir_1}, {screener_dir_2}, and {screener_dir_3}")

    if not file_list:
        print("No files found with the pattern 'screener_results*.xls'")
        return

    df = pd.DataFrame()

    # Concatenate all files matching the pattern
    for file in file_list:
        df_temp = pd.read_excel(file)
        df = pd.concat([df, df_temp], ignore_index=True)

    # Drop duplicate rows based on the 'Symbol' column
    df = df.drop_duplicates(subset='Symbol')

    # add $volume to df, defined as Volume (10 Day Avg) in millions * df["Security Price"]

    df['$Volume'] = df["Volume (10 Day Avg)"]  * df["Security Price"]

    # Filter out rows where $Volume is less than 25
    df = df[df['$Volume'] >= 25]

    #print(df[['Symbol', '$Volume']].head(25))


    tickers = df["Symbol"].to_list()

    printFilteredData(tickers)


    filter = input("Earnings Filter needed(Y/N):")

    # Ask for RS rating filter
    rs_filter_input = input("RS Rating filter (enter minimum RS rating 1-99, or press Enter to skip): ").strip()
    rs_rating_filter = None
    if rs_filter_input:
        try:
            rs_rating_filter = int(rs_filter_input)
            if rs_rating_filter < 1 or rs_rating_filter > 99:
                print("Invalid RS rating. Must be between 1-99. Skipping RS filter.")
                rs_rating_filter = None
        except ValueError:
            print("Invalid input. Skipping RS filter.")
            rs_rating_filter = None

    # Filter the screener DataFrame with CSV data
    screener = groupAndFilter(df, filter, rs_rating_filter)

    printFilteredData( tickers = screener["Symbol"].to_list())

    #done = ["RDDT","ALAB"]
    #screener = screener[screener['Symbol'].isin(done)]

    df_sorted = screener

    tickers = df_sorted["Symbol"].to_list()

    printFilteredData(tickers)


    print("Start? (Y/N):")

    start = input()


    if start.strip().upper() == 'Y':
       df_sorted.to_excel("sorted_screener.xlsx")
       # Pipeline: store dataset and launch UI for analysis anytime
       save_file = input('Generate DOCX too? (Y/N): ').strip().lower() == 'y'
       asyncio.run(downloadChartsFidelityV2(df_sorted, "fidelity_screen" if save_file else None))

chartAnalysis()
