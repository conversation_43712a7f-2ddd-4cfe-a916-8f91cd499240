"""
Test script to verify that the SSL certificate verification fix works for all problematic APIs.
"""

import sys
import os
import warnings

# Add src directory to Python path
sys.path.append('src')

# Disable SSL verification warnings
warnings.filterwarnings('ignore', message='Unverified HTTPS request')

# Set environment variables for SSL certificate verification
os.environ['SSL_CERT_FILE'] = os.path.join(os.path.dirname(__file__), 'src', 'certs', 'my_ca_bundle.pem')
os.environ['REQUESTS_CA_BUNDLE'] = os.environ['SSL_CERT_FILE']
os.environ['CURL_CA_BUNDLE'] = os.environ['SSL_CERT_FILE']

# Import the functions that were having SSL certificate verification issues
from util.cryp_free_greed import plot as crypto_fear_greed_plot
from util.cnn_fear_greed import CNNGreedFear
from common.http_session import get_session

def test_crypto_fear_greed():
    """Test the crypto fear and greed index API."""
    print("\n=== Testing Crypto Fear and Greed API ===")
    
    try:
        # Import here to avoid circular imports
        import os
        import requests
        from dotenv import load_dotenv
        
        # Load environment variables
        load_dotenv()
        
        # Get API key from environment variables
        API_KEY = os.getenv('CMC_PRO_API_KEY')
        url = f"https://pro-api.coinmarketcap.com/v3/fear-and-greed/historical?api_key={API_KEY}"
        
        # Create a session with SSL verification disabled
        session = get_session(use_standard_requests=True, disable_warnings=True)
        
        # Make the request with our custom session
        response = session.get(url)
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success! Status code: {response.status_code}")
            print(f"Data contains {len(data.get('data', []))} entries")
            return True
        else:
            print(f"Error: Status code {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_cnn_fear_greed():
    """Test the CNN fear and greed index API."""
    print("\n=== Testing CNN Fear and Greed API ===")
    
    try:
        # Import here to avoid circular imports
        import requests
        
        # Fetching data from the API
        url = "https://production.dataviz.cnn.io/index/fearandgreed/graphdata"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
        }
        
        # Create a session with SSL verification disabled
        session = get_session(use_standard_requests=True, disable_warnings=True)
        
        # Make the request with our custom session
        response = session.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"Success! Status code: {response.status_code}")
            print(f"Data contains fear_and_greed_historical: {bool(data.get('fear_and_greed_historical'))}")
            return True
        else:
            print(f"Error: Status code {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all SSL certificate verification tests."""
    results = {}
    
    # Test crypto fear and greed
    results['crypto_fear_greed'] = test_crypto_fear_greed()
    
    # Test CNN fear and greed
    results['cnn_fear_greed'] = test_cnn_fear_greed()
    
    # Print summary
    print("\n=== TEST SUMMARY ===")
    for test_name, success in results.items():
        print(f"{test_name}: {'SUCCESS' if success else 'FAILURE'}")
    
    # Return True if all tests passed
    return all(results.values())

if __name__ == "__main__":
    print("Starting SSL certificate verification tests...")
    result = run_all_tests()
    print(f"\nOverall test result: {'SUCCESS' if result else 'FAILURE'}")
