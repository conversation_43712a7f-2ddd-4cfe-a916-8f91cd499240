#!/usr/bin/env python3
"""
Test script to verify the refactored yfinance fallback to daily EOD data.
"""

import asyncio
import sys
import os
import pandas as pd

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ticker_analyzer.data_fetcher import _fallback_to_yfinance_eod

async def test_daily_eod_fallback(ticker='AFRM'):
    """Test the refactored yfinance fallback to daily EOD data."""
    print(f"Testing refactored yfinance fallback with daily EOD data for {ticker}...")

    result = await _fallback_to_yfinance_eod(ticker)

    if result is None or result.empty:
        print("❌ Fallback failed: No data returned.")
        return False

    print(f"✅ Success: {len(result)} total records")
    print(f"Columns: {list(result.columns)}")
    print(f"Date range: {result.index[0].date()} to {result.index[-1].date()}")

    # Verify required columns are present
    expected_columns = ['open_price', 'high_price', 'low_price', 'close_price', 'volume']
    if not all(col in result.columns for col in expected_columns):
        print(f"❌ FAILED: Missing one or more expected columns. Found: {list(result.columns)}")
        return False

    print(f"Latest close: ${result['close_price'].iloc[-1]:.2f}")
    print(f"Latest volume: {result['volume'].iloc[-1]:,.0f}")

    # Check if the index is a DatetimeIndex
    if not isinstance(result.index, pd.DatetimeIndex):
        print("❌ FAILED: Index is not a DatetimeIndex.")
        return False

    print("\n✅ SUCCESS: Data structure is valid.")
    print("🎉 Fallback to daily EOD data is working correctly!")

    return True

if __name__ == "__main__":
    ticker = sys.argv[1] if len(sys.argv) > 1 else 'AFRM'

    async def main():
        print(f"Testing refactored yfinance fallback (daily EOD) with {ticker}")
        print("=" * 70)

        success = await test_daily_eod_fallback(ticker)

        if success:
            print(f"\n🎉 Daily EOD fallback test completed successfully!")
        else:
            print(f"\n❌ Daily EOD fallback test failed.")

    asyncio.run(main())
