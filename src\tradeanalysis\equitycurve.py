import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import os
import numpy as np

def load_trades_data():
    """
    Loads trade data from Excel, falling back to CSV if necessary.
    """
    data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'trade_analysis')
    excel_path = os.path.join(data_dir, 'trades.xlsx')
    csv_path = os.path.join(data_dir, 'trades.csv')

    try:
        print(f"Attempting to load data from {excel_path}...")
        df = pd.read_excel(excel_path)
        print("Successfully loaded from Excel.")
    except (FileNotFoundError, PermissionError) as e:
        print(f"Could not load from Excel ({e}), falling back to CSV.")
        try:
            df = pd.read_csv(csv_path)
            print("Successfully loaded from CSV.")
        except FileNotFoundError:
            print(f"Error: Neither {excel_path} nor {csv_path} could be found.")
            return None

    return df

def calculate_statistics(df):
    """
    Calculates and returns key performance statistics.
    """
    if df.empty:
        return None

    stats = {}

    total_trades = len(df)
    if total_trades == 0:
        return {'Total Trades': 0}

    wins = df[df['P/L'] > 0]
    losses = df[df['P/L'] <= 0]

    stats['Total Trades'] = total_trades
    stats['Win Rate'] = len(wins) / total_trades if total_trades > 0 else 0
    stats['Loss Rate'] = len(losses) / total_trades if total_trades > 0 else 0

    stats['Average Win'] = wins['P/L'].mean() if not wins.empty else 0
    stats['Average Loss'] = losses['P/L'].mean() if not losses.empty else 0

    gross_profit = wins['P/L'].sum()
    gross_loss = abs(losses['P/L'].sum())

    stats['Profit Factor'] = gross_profit / gross_loss if gross_loss != 0 else np.inf

    win_loss_ratio = abs(stats['Average Win'] / stats['Average Loss']) if stats['Average Loss'] != 0 else np.inf
    stats['Win/Loss Ratio'] = win_loss_ratio

    expectancy = (stats['Win Rate'] * stats['Average Win']) + (stats['Loss Rate'] * stats['Average Loss'])
    stats['Expectancy (per trade)'] = expectancy

    return stats

def plot_equity_curve(df, stats):
    """
    Plots the equity curve and max drawdown.
    """
    # Prepare data for plotting
    df['SellDate'] = pd.to_datetime(df['SellDate'])
    df.sort_values(by='SellDate', inplace=True)
    df['Cumulative P/L'] = df['P/L'].cumsum()

    # Calculate a 7-day moving average of the equity curve
    # We need to set the index to the date for rolling calculations
    equity_curve_df = df.set_index('SellDate')['Cumulative P/L']
    df['Equity MA'] = equity_curve_df.rolling(window='7D').mean().values

    # Calculate Max Drawdown based on the smoothed curve
    df['Peak MA'] = df['Equity MA'].cummax()
    df['Drawdown MA'] = df['Peak MA'] - df['Equity MA']
    max_drawdown_ma = df['Drawdown MA'].max()
    stats['Max Drawdown'] = max_drawdown_ma # Reporting the smoothed drawdown

    # Plotting
    plt.figure(figsize=(15, 8))
    plt.plot(df['SellDate'], df['Equity MA'], label='7-Day Rolling Average Equity', linestyle='-', color='orange', linewidth=2)
    plt.plot(df['SellDate'], df['Peak MA'], label='Peak of 7-Day MA', linestyle='--', color='gray')

    # The red shaded area now represents the drawdown from the peak of the smoothed average curve.
    plt.fill_between(df['SellDate'], df['Equity MA'], df['Peak MA'], color='red', alpha=0.2, label=f'Smoothed Drawdown (Max: ${max_drawdown_ma:.2f})')

    plt.title('Smoothed Equity Curve (7-Day Rolling Average)')
    plt.xlabel('Date')
    plt.ylabel('Cumulative P/L ($)')
    plt.grid(True)
    plt.legend()

    # Format x-axis
    plt.gcf().autofmt_xdate()
    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))

    plt.tight_layout()
    plt.show()

def main():
    """
    Main function to run the equity curve analysis.
    """
    df = load_trades_data()
    if df is None or df.empty:
        print("No trade data to analyze.")
        return

    # Filter out open trades if any
    df = df[(df['SellPrice'] != 0) & (df['BuyPrice'] != 0)].copy()

    stats = calculate_statistics(df)

    if stats:
        print("\n--- Trading Performance Statistics ---")
        print(f"Total Trades: {stats['Total Trades']}")
        print(f"Win Rate: {stats['Win Rate']:.2%}")
        print(f"Loss Rate: {stats['Loss Rate']:.2%}")
        print(f"Average Win: ${stats['Average Win']:.2f}")
        print(f"Average Loss: ${stats['Average Loss']:.2f}")
        print(f"Win/Loss Ratio: {stats['Win/Loss Ratio']:.2f}")
        print(f"Profit Factor: {stats['Profit Factor']:.2f}")
        print(f"Expectancy (per trade): ${stats['Expectancy (per trade)']:.2f}")
        print("--------------------------------------\n")

        plot_equity_curve(df, stats)

        # Print Max Drawdown after plotting has calculated it
        print(f"Max Drawdown: ${stats['Max Drawdown']:.2f}")

if __name__ == "__main__":
    main()
