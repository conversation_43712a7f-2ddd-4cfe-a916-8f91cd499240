# api_headers.py - Provides headers for Fidelity API requests

import requests
from dotenv import load_dotenv
import os
import re

load_dotenv()

cookie = os.getenv('FIDELITY_COOKIE')

cookie_scan = os.getenv('FIDELITY_COOKIE_SCAN')

def get_csrf_token(cookie: str) -> str:
    """Fetch the CSRF token"""
    url = "https://digital.fidelity.com/prgw/digital/research/api/tokens"
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.9',
        'cookie': cookie,
        'dnt': '1',
        'priority': 'u=1, i',
        'referer': 'https://digital.fidelity.com/prgw/digital/research/quote?symbol=NVDA',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'sec-gpc': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }

    response = requests.get(url, headers=headers)
    response.raise_for_status()  # Raise an error if the request failed

    # Assuming the response JSON structure is {'node': 'csrfTokenValue'}, adjust if different
    return response.json().get('csrfToken')

def get_fidelity_headers() -> dict:

    csrf_token = get_csrf_token(cookie)

    return {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'apollographql-client-version': '0.0.0',
        'content-type': 'application/json',
        'cookie': cookie,
        'dnt': '1',
        'origin': 'https://digital.fidelity.com',
        'priority': 'u=1, i',
        'referer': 'https://digital.fidelity.com/ftgw/digital/watwebex',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'sec-gpc': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Host': 'digital.fidelity.com',
        'x-csrf-token': csrf_token
    }

def get_ctoken(cookie: str) -> str:
    """Fetch the ctoken from the poswebex snippet."""
    url = "https://digital.fidelity.com/ftgw/digital/positions/poswebex/?snippet=Y"
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.9",
        "appid": "AP143528",
        "appname": "Portsum Dashboard",
        "cookie": cookie,
        "dnt": "1",
        "priority": "u=1, i",
        "referer": "https://digital.fidelity.com/ftgw/digital/portfolio/positions",
        "sec-ch-ua": '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "whereused": "https://digital.fidelity.com/ftgw/digital/portfolio/positions",
    }
    response = requests.get(url, headers=headers)
    response.raise_for_status()

    # The response is HTML containing a script tag with the ctoken.
    # We need to use regex to extract it.
    match = re.search(r"ctoken:\s*'([^']+)'", response.text)
    if match:
        return match.group(1)
    raise ValueError("Could not find ctoken in the response from poswebex.")

def get_positions_headers(cookie: str) -> dict:

    csrf_token = get_ctoken(cookie)
    """Headers for the positions API"""
    return {
        'accept': 'application/json, text/html',
        'accept-language': 'en-US,en;q=0.9',
        'content-type': 'application/json',
        'dnt': '1',
        'origin': 'https://digital.fidelity.com',
        'preset-view': 'StackOverView',
        'priority': 'u=1, i',
        'referer': 'https://digital.fidelity.com/ftgw/digital/portfolio/positions',
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-csrf-token': csrf_token,
        'cookie': cookie,
    }

def get_screnner_headers() -> dict:
    return {
    "Accept": "application/json, text/javascript, */*; q=0.01",
    "Accept-Language": "en-US,en;q=0.9",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
    "Cookie": cookie,
    "DNT": "1",
    "Origin": "https://research2.fidelity.com",
    "Referer": "https://research2.fidelity.com/pi/stock-screener",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "X-Requested-With": "XMLHttpRequest",
    "sec-ch-ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-gpc": "1",
}

def get_fidelity_headers_v2() -> dict:

     csrf_token = get_csrf_token(cookie_scan)

     return {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9',
        'apollographql-client-version': '0.0.0',
        'content-type': 'application/json',
        'cookie': cookie_scan,
        'dnt': '1',
        'origin': 'https://digital.fidelity.com',
        'priority': 'u=1, i',
        'referer': 'https://digital.fidelity.com/ftgw/digital/watwebex',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'sec-gpc': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Host': 'digital.fidelity.com',
        'x-csrf-token': csrf_token
    }
