import os
import sys
import webbrowser
import pandas as pd

# Import the new modules
from . import data_manager as dm
from . import web_ui as wu


def load_latest_snapshot_df():
    """Load the latest filtered_sorted_screener_YYYY-MM-DD.xlsx from SCREENER_DIR."""
    date_str = dm._get_snapshot_date_yyyy_mm_dd()
    snap_path = os.path.join(dm.SCREENER_DIR, f"filtered_sorted_screener_{date_str}.xlsx")
    if not os.path.exists(snap_path):
        raise FileNotFoundError(
            f"Snapshot not found: {snap_path}. Run the weekly prep to generate it.")
    df = pd.read_excel(snap_path)
    # Ensure minimal columns exist; otherwise just proceed with whatever is present
    return date_str, df


def main():
    try:
        # Ensure data directories exist
        dm._ensure_data_dirs()

        # Load latest snapshot
        snapshot_date, df = load_latest_snapshot_df()
        wu.CURRENT_TICKERS = df.to_dict(orient='records')

        # Prompt for chart IDs every session (no persistence)
        daily_id = input("Enter Daily Chart ID: ").strip()
        weekly_id = input("Enter Weekly Chart ID: ").strip()
        if not daily_id or not weekly_id:
            print("Both Daily and Weekly Chart IDs are required.")
            sys.exit(1)

        wu.DAILY_CHART_ID = daily_id
        wu.WEEKLY_CHART_ID = weekly_id

        # Start Like + UI servers
        wu.start_like_server()
        ui_url = f"http://127.0.0.1:{wu.LIKE_SERVER_PORT+1}/app"
        print(f"Loaded snapshot date: {snapshot_date}")
        print(f"Likes file: {dm.LIKES_OUTPUT_PATH}")
        print(f"Opening UI: {ui_url}")
        webbrowser.open(ui_url)

        # Keep process alive until user exits
        input("UI is running. Press Enter here to stop and exit... ")

    except Exception as e:
        print(f"ERROR: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
