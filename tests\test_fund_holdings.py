import sys
import os

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from util.scrapper import fundHoldingsYahoo

def test_fund_holdings():
    """Test the fundHoldingsYahoo function with AMD ticker."""
    ticker = 'AMD'
    print(f"Testing fundHoldingsYahoo with ticker: {ticker}")

    # Call the function
    result = fundHoldingsYahoo(ticker)

    # Print the result
    if result is not None and not result.empty:
        print(f"Success! Found {len(result)} rows of institutional ownership data.")
        print("\nFirst few rows:")
        print(result.head())
    else:
        print("No data returned or empty DataFrame.")

if __name__ == "__main__":
    test_fund_holdings()
