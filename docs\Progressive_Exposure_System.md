# Progressive Exposure Sizing System

## 1. Overview

This document outlines the logic for the automated position sizing recommendation system. The system's goal is to provide a data-driven recommendation for position size ("Full", "Half", or "Quarter") based on a trader's recent performance, normalized for risk.

## 2. Core Concepts

The system is built on a hybrid model called **"Momentum-Adjusted Volatility-Scaled Drawdown,"** which integrates three key concepts:

1.  **R-Multiple Normalization:** All trades are measured in terms of "R," where 1R is the initial planned risk on a trade. This makes all trades comparable, regardless of their dollar size.
2.  **Volatility-Adjusted Drawdown:** The system's sensitivity to drawdowns is scaled by the historical volatility of the trader's R-Multiple returns. This makes the system adaptive to the strategy's unique performance signature.
3.  **Time-Based Momentum:** A recent performance "kicker" is calculated based on the average R-Multiple of trades over a fixed time window (e.g., 30 days).

## 3. The Recommendation Logic

The system follows a clear, hierarchical rule set to generate its recommendation:

### Step 1: Data Preparation

1.  Load the 90-day trade history from `trades.xlsx` (with a fallback to `trades.csv`).
2.  Calculate the R-Multiple for each trade: `R-Multiple = P/L / (BuyPrice * Quantity * 0.08)`.
3.  Calculate the cumulative equity curve in R (`Cumulative R`).

### Step 2: Calculate Key Metrics

1.  **Adaptive R-Volatility:** Calculate the rolling standard deviation of the `R-Multiple` over a 50-trade window.
2.  **Current R-Drawdown:** Calculate the current drawdown from the peak of the `Cumulative R` curve.
3.  **Recent R-Momentum:** Calculate the average `R-Multiple` of all trades within the last 30 calendar days.

### Step 3: The Sizing Rules

The system uses the following rules, in order of precedence:

1.  **IF** the `Current R-Drawdown` is greater than **3.0 times** the `Adaptive R-Volatility`:
    *   **Recommendation: "Quarter"**
    *   *Reasoning: The system is in a statistically significant drawdown. Capital preservation is the top priority.*

2.  **ELSE IF** the `Current R-Drawdown` is greater than **1.5 times** the `Adaptive R-Volatility`:
    *   **IF** the `Recent R-Momentum` is **positive** (and based on at least 3 trades):
        *   **Recommendation: "Half"**
        *   *Reasoning: The system is in a modest drawdown, but recent performance is strong, suggesting a potential recovery.*
    *   **ELSE:**
        *   **Recommendation: "Quarter"**
        *   *Reasoning: The system is in a modest drawdown, and recent performance is weak or insignificant. Risk must be reduced.*

3.  **ELSE** (the system is at or near its peak equity):
    *   **IF** the `Recent R-Momentum` is **positive** (and based on at least 3 trades):
        *   **Recommendation: "Full"**
        *   *Reasoning: The system is performing well, and recent momentum is strong. It's time to press the advantage.*
    *   **ELSE:**
        *   **Recommendation: "Half"**
        *   *Reasoning: The system is performing well overall, but recent performance has been weak. A signal for prudence.*

## 4. Configuration

The key parameters of the system are configurable in the `get_progressive_exposure_recommendation` function in `src/tradeanalysis/performance_analyzer.py`:

*   `modest_dd_multiplier`: 1.5
*   `significant_dd_multiplier`: 3.0
*   `volatility_window`: 50
*   `momentum_window_days`: 30
*   `min_trades_for_momentum`: 3
