/* General styles from /app */
:root {
  --gap: 14px;
  --pad: 12px;
  --radius: 10px;
  --border: #d9d9d9;
  --text: #111;
  --muted: #444;
}

html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: system-ui, -apple-system, Segoe UI, Roboto, Arial, sans-serif;
  color: var(--text);
  background: #fafafa;
}

body.card-only {
  overflow: hidden;
}

/* Header styles from /app */
header {
  position: sticky;
  top: 0;
  z-index: 5;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  background: #1f1f1f;
  color: #fff;
  border-bottom: 1px solid #000;
  flex-wrap: wrap;
}

header button, header a {
  font-size: clamp(12px, 1.6vw, 14px);
}

/* Filter styles from /app */
.filters {
  display: flex;
  gap: 8px;
  align-items: center;
}

.filters select {
  background: #333;
  color: #fff;
  border: 1px solid #555;
  border-radius: 4px;
  padding: 4px 6px;
  font-size: clamp(11px, 1.4vw, 13px);
  min-width: 120px;
}

.filters select:focus {
  outline: 2px solid #9cf;
}

/* Grid and Card styles from /app */
.grid {
  padding: var(--gap);
  display: flex;
  flex-wrap: wrap;
  gap: var(--gap);
  justify-content: center;
}

body.card-only .grid {
  height: calc(100svh - 42px);
  padding: 0;
  margin: 0;
  display: grid;
  place-items: center;
  overflow: hidden;
}

.card {
  width: min(1400px, 98vw);
  height: calc(100svh - 60px);
  box-sizing: border-box;
  background: #fff;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: var(--pad);
  display: grid;
  grid-template-rows: auto 1fr auto auto;
  gap: var(--gap);
  box-shadow: 0 1px 10px rgba(0,0,0,.04);
  min-height: 0;
  overflow: hidden;
}

.title {
  font-weight: 700;
  letter-spacing: .2px;
  font-size: clamp(18px, 2.6vw, 24px);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--gap);
  align-items: stretch;
  min-height: 0;
  overflow: hidden;
  height: 100%;
}

.chart {
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 6px;
  display: grid;
  grid-template-rows: auto 1fr;
  gap: 6px;
  background: #fff;
  min-height: 0;
  overflow: hidden;
}

.chart header {
  position: static;
  background: transparent;
  color: var(--muted);
  padding: 0;
  border: 0;
  font-size: clamp(12px, 1.6vw, 14px);
  flex-shrink: 0;
}

.chart figure {
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 0;
  overflow: hidden;
  position: relative;
}

.chart img {
  display: block;
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border: 1px solid #e9e9e9;
  border-radius: 6px;
  background: #fff;
  transition: opacity 0.3s ease;
}

.chart img.loading {
  opacity: 0.7;
}

.chart img.loaded {
  opacity: 1;
}

.info-wrap {
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 8px;
  background: #fff;
  max-height: 20vh;
  overflow: auto;
  flex-shrink: 0;
}

table.info {
  width: 100%;
  border-collapse: collapse;
  font-size: clamp(12px, 1.7vw, 14px);
}

.info th, .info td {
  padding: 4px 6px;
  vertical-align: top;
  border-bottom: 1px dashed #eee;
}

.info th {
  text-align: left;
  white-space: nowrap;
  color: var(--muted);
  width: 32%;
}

.info tr:last-child th, .info tr:last-child td {
  border-bottom: 0;
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  flex-shrink: 0;
}

button {
  border: 1px solid #cfcfcf;
  background: #f7f7f7;
  border-radius: 6px;
  padding: 8px 10px;
  cursor: pointer;
  font-size: clamp(12px, 1.7vw, 14px);
}

button:hover {
  background: #f1f1f1;
}

body.card-only .grid {
  overflow: hidden;
}

body.card-only .card {
  width: 98vw;
}

@media (max-width: 820px) {
  .charts {
    grid-template-columns: 1fr;
  }
  .info-wrap {
    max-height: 25vh;
  }
  .card {
    height: calc(100svh - 50px);
  }
}

@media (max-height: 600px) {
  .info-wrap {
    max-height: 15vh;
  }
  .info th, .info td {
    padding: 2px 4px;
  }
}

/* Styles from /chart page */
.header {
  background: #1f2937;
  color: white;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header h1 {
  margin: 0;
  font-size: 1.5rem;
}

.header a {
  color: #60a5fa;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}

.header a:hover {
  background-color: #374151;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #1e293b;
}

.stat-label {
  color: #64748b;
  font-size: 0.875rem;
}

.chart-section {
  background: white;
  margin-bottom: 2rem;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.chart-section h2 {
  margin: 0 0 1rem 0;
  color: #1e293b;
  font-size: 1.25rem;
}

.bar-chart {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.bar-row {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 1rem;
  align-items: center;
}

.bar-label {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.bar-container {
  position: relative;
  background: #f1f5f9;
  border-radius: 0.25rem;
  height: 1.5rem;
}

.bar-fill {
  height: 100%;
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

.bar-count {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.75rem;
  color: #475569;
  font-weight: 600;
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  .bar-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  .bar-label {
    font-size: 0.75rem;
  }
}
