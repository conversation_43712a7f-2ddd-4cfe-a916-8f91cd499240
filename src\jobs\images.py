import csv
import time
from PIL import Image
import docx
import os
from common.http_session import get_session

headers = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36'}


def showImg():
    doc = docx.Document()
    images_dir = 'src/data/images'

    # opening the CSV file path = 'src/data/'
    with open('src/data/urls.csv', mode='r')as file:

      # reading the CSV file
      csvFile = csv.reader(file)
      for idx, val in enumerate(csvFile):
         image_path = os.path.join(images_dir, f'images{idx}.png')

         # Check if image file exists before trying to open it
         if os.path.exists(image_path):
             try:
                 Image.open(image_path)
                 doc.add_picture(image_path)
             except Exception as e:
                 print(f"Error processing image {image_path}: {e}")
         else:
             print(f"Image file not found: {image_path}")

    doc.save('market.docx')
    os.startfile('market.docx')


def dwnloadImg():
    # Create images directory if it doesn't exist
    images_dir = 'src/data/images'
    os.makedirs(images_dir, exist_ok=True)

    # opening the CSV file path = 'src/data/'
    with open('src/data/urls.csv', mode='r')as file:

      # reading the CSV file
      csvFile = csv.reader(file)

      # displaying the contents of the CSV file
      for idx, val in enumerate(csvFile):
          image_path = os.path.join(images_dir, f'images{idx}.png')
          with open(image_path, 'wb') as handle:
              url = val[0].replace('tdy', str(time.time()))
              time.sleep(2)

              try:
                  # First try with curl_cffi session with stream mode
                  session = get_session()
                  response = session.get(url, headers=headers, stream=True)

                  if not response.ok:
                      print(f"Response error for {url}: {response}")
                      continue

                  # For curl_cffi, we need to handle content differently
                  if hasattr(response, 'iter_content'):
                      for block in response.iter_content(1024):
                          if not block:
                              break
                          handle.write(block)
                  else:
                      # Fallback for curl_cffi without stream support
                      handle.write(response.content)

              except Exception as e:
                  if "stream mode is not enabled" in str(e):
                      print(f"Stream mode error, falling back to standard requests for {url}")
                      # Fall back to standard requests
                      session = get_session(use_standard_requests=True)
                      response = session.get(url, headers=headers, stream=True)

                      if not response.ok:
                          print(f"Response error for {url}: {response}")
                          continue

                      for block in response.iter_content(1024):
                          if not block:
                              break
                          handle.write(block)
                  else:
                      print(f"Error downloading {url}: {e}")
                      continue

import webbrowser

def open_webpage(url):
    webbrowser.open(url)

# if __name__ == "__main__":
#     open_webpage("https://www.forexfactory.com/calendar?week=this")




# dwnloadImg()
# showImg()
# NAAIMExp()
#downloadCharts(['AMZN'])
