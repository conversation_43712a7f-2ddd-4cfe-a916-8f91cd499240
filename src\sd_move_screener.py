import pandas as pd
import sys
import os
import asyncio
import aiohttp

# Add the project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from stockdata.data_source import getStockDataV3

async def screen_sd_move():
    """
    Scans a list of stock symbols to identify any with a standard deviation move greater than 1
    across 30, 60, or 90-day timeframes based on the most recent trading day.
    """
    symbols = [
        'XLB', 'IBB', 'XLY', 'GDX', 'IYT', 'XLE', 'XLF', 'XLV',
        'XHB', 'XLI', 'XME', 'XBI', 'MAGS', 'XRT', 'SMH', 'XLK', 'XLU', 'KWEB',
        'GLD', 'XLC', 'UUP', 'TLT', 'HYG', 'IBIT', 'SKYY', 'BOTZ', 'ROBT', 'QTUM',
        'KRE', 'URA', 'URNM', 'XLP', 'XLRE', 'IYR', 'XOP', 'NUKZ', 'IGV', 'FFTY',
        'IPO', 'UFO', 'VNQ', 'ETHA','IWM','SPY','QQQ'
    ]

    print("Starting SD Move Screener...")

    async with aiohttp.ClientSession() as session:
        for symbol in symbols:
            try:
                # Fetch data with SD move calculations
                df = await getStockDataV3(session, symbol)

                if df is None or df.empty:
                    # print(f"No data for {symbol}")
                    continue



                # Check for SD move significance
                sd_move_30d = df['SD_Move_30D'].iloc[0]
                sd_move_60d = df['SD_Move_60D'].iloc[0]
                sd_move_90d = df['SD_Move_90D'].iloc[0]

                if sd_move_30d > 1 or sd_move_60d > 1 or sd_move_90d > 1:
                    print(f"----------------------------------------")
                    print(f"Symbol: {symbol}")
                    print(f"  SD Move 30D: {sd_move_30d}")
                    print(f"  SD Move 60D: {sd_move_60d}")
                    print(f"  SD Move 90D: {sd_move_90d}")

            except Exception as e:
                print(f"Could not process {symbol}: {e}")

    print("----------------------------------------")
    print("Screener finished.")

if __name__ == "__main__":
    # To fix a known issue with asyncio on Windows
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(screen_sd_move())
