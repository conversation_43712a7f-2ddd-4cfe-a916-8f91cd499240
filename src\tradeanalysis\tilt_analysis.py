import pandas as pd
import numpy as np
import os

def load_trades_data():
    """
    Loads trade data from Excel, falling back to CSV if necessary.
    """
    data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'trade_analysis')
    excel_path = os.path.join(data_dir, 'trades.xlsx')
    csv_path = os.path.join(data_dir, 'trades.csv')

    try:
        print(f"Attempting to load data from {excel_path}...")
        df = pd.read_excel(excel_path)
        print("Successfully loaded from Excel.")
    except (FileNotFoundError, PermissionError) as e:
        print(f"Could not load from Excel ({e}), falling back to CSV.")
        try:
            df = pd.read_csv(csv_path)
            print("Successfully loaded from CSV.")
        except FileNotFoundError:
            print(f"Error: Neither {excel_path} nor {csv_path} could be found.")
            return None

    return df

def analyze_tilt(df, loss_percentile=0.25, trades_after_loss=3):
    """
    Analyzes trading behavior after large losses to identify 'tilt'.
    """
    if df.empty:
        print("No trades to analyze.")
        return

    # Ensure data is sorted by date
    df['SellDate'] = pd.to_datetime(df['SellDate'])
    df.sort_values(by='SellDate', inplace=True)
    df.reset_index(drop=True, inplace=True)

    losses = df[df['P/L'] < 0]['P/L']
    if losses.empty:
        print("No losses found, no tilt analysis possible.")
        return

    # Define a "large loss"
    large_loss_threshold = losses.quantile(loss_percentile)
    large_loss_indices = df[df['P/L'] <= large_loss_threshold].index

    print(f"\nIdentifying large losses (below {loss_percentile:.0%} percentile: ${large_loss_threshold:.2f})...")
    print(f"Found {len(large_loss_indices)} large losses.")

    post_loss_trades = []
    for i in large_loss_indices:
        # Get the next N trades, ensuring we don't go out of bounds
        start_index = i + 1
        end_index = start_index + trades_after_loss
        if start_index < len(df):
            post_loss_trades.extend(df.iloc[start_index:end_index].to_dict('records'))

    if not post_loss_trades:
        print("No trades found after large losses to analyze.")
        return

    post_loss_df = pd.DataFrame(post_loss_trades)

    # --- Compare Statistics ---
    print("\n--- Tilt Analysis ---")

    # Overall stats
    overall_win_rate = len(df[df['P/L'] > 0]) / len(df)
    overall_avg_pl = df['P/L'].mean()

    # Post-loss stats
    post_loss_win_rate = len(post_loss_df[post_loss_df['P/L'] > 0]) / len(post_loss_df)
    post_loss_avg_pl = post_loss_df['P/L'].mean()

    print(f"Comparing the {len(post_loss_df)} trades immediately following large losses to the overall average:")
    print("\nWin Rate:")
    print(f"  - Overall: {overall_win_rate:.2%}")
    print(f"  - Post-Large-Loss: {post_loss_win_rate:.2%}")

    print("\nAverage P/L per Trade:")
    print(f"  - Overall: ${overall_avg_pl:.2f}")
    print(f"  - Post-Large-Loss: ${post_loss_avg_pl:.2f}")

    # Tilt Score (a simple example)
    # A score > 1 might suggest negative 'tilt' behavior (worse performance)
    # A score < 1 might suggest positive 'tilt' behavior (better performance, or more caution)
    if overall_avg_pl != 0:
        tilt_score = post_loss_avg_pl / overall_avg_pl
        print(f"\nTilt Score (Post-Loss P/L / Overall P/L): {tilt_score:.2f}")
        if tilt_score < 1:
            print("Interpretation: Performance tends to be worse after a large loss (potential negative tilt).")
        else:
            print("Interpretation: Performance tends to be better after a large loss (potential positive reaction).")

def main():
    """
    Main function to run the tilt analysis.
    """
    df = load_trades_data()
    if df is None or df.empty:
        return

    df = df[(df['SellPrice'] != 0) & (df['BuyPrice'] != 0)].copy()

    analyze_tilt(df)

if __name__ == "__main__":
    main()
