import os
import time
import docx
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
from docx.opc.constants import RELATIONSHIP_TYPE
from common.http_session import get_session
from .data_manager import LIKES_OUTPUT_PATH

LIKE_SERVER_PORT = 5001

def add_hyperlink(paragraph, url, text):
    """Add a hyperlink to a paragraph in a python-docx document."""
    part = paragraph.part
    r_id = part.relate_to(url, RELATIONSHIP_TYPE.HYPERLINK, is_external=True)
    hyperlink = OxmlElement('w:hyperlink')
    hyperlink.set(qn('r:id'), r_id)
    new_run = OxmlElement('w:r')
    r_pr = OxmlElement('w:rPr')
    u = OxmlElement('w:u')
    u.set(qn('w:val'), 'single')
    color = OxmlElement('w:color')
    color.set(qn('w:val'), '0000FF')
    r_pr.append(u)
    r_pr.append(color)
    new_run.append(r_pr)
    t = OxmlElement('w:t')
    t.text = text
    new_run.append(t)
    hyperlink.append(new_run)
    paragraph._p.append(hyperlink)
    return hyperlink

def download_image(url, file_path):
    """Download an image from a URL and save it to a file."""
    try:
        try:
            session = get_session()
            response = session.get(url, stream=True)
        except Exception as curl_error:
            if "stream mode is not enabled" in str(curl_error) or True:
                print(f"Falling back to standard requests due to: {curl_error}")
                session = get_session(use_standard_requests=True)
                response = session.get(url, stream=True)
        if not response.ok:
            print(f"Request failed with status code: {response.status_code}")
            return False
        with open(file_path, 'wb') as handle:
            for block in response.iter_content(1024):
                if not block:
                    break
                handle.write(block)
        return True
    except Exception as e:
        print(f"Error downloading image from {url}: {e}")
        return False

def add_image_to_doc(doc, file_path, clean_image=False):
    """Add an image to a Word document and then remove the image file."""
    try:
        doc.add_picture(file_path)
    except Exception as e:
        print(f"Error adding image {file_path} to document: {e}")
    finally:
        if clean_image:
            os.remove(file_path)

def create_vertical_table(doc, sector, industry, sub_industry, forward_eps_growth,
                           eps_growth_last_qtr, eps_growth_ttm_vs_prior_ttm, eps_growth_3_year, eps_growth_5_year,
                           eps_growth_proj_next_yr_vs_this_yr, revenue_growth_ttm_vs_prior_ttm, revenue_growth_last_qtr,
                           equity_summary_score):
    table = doc.add_table(rows=14, cols=2)
    table.autofit = True
    table_data = [
        ('Sector:', f'{sector}'),
        ('Industry:', f'{industry}'),
        ('Sub-Industry:', f'{sub_industry}'),
        ('Forward EPS Long Term Growth:', f'{forward_eps_growth}'),
        ('EPS Growth Last Qtr:', f'{eps_growth_last_qtr}'),
        ('EPS Growth TTM vs Prior TTM:', f'{eps_growth_ttm_vs_prior_ttm}'),
        ('EPS Growth 3 Year:', f'{eps_growth_3_year}'),
        ('EPS Growth 5 Year:', f'{eps_growth_5_year}'),
        ('EPS Growth Proj Next Yr vs This Yr:', f'{eps_growth_proj_next_yr_vs_this_yr}'),
        ('Revenue Growth TTM vs Prior TTM:', f'{revenue_growth_ttm_vs_prior_ttm}'),
        ('Revenue Growth Last Qtr:', f'{revenue_growth_last_qtr}'),
        ('Equity Summary Score:', f'{equity_summary_score}')
    ]
    for row_idx, (label, value) in enumerate(table_data):
        table.cell(row_idx, 0).text = label
        table.cell(row_idx, 1).text = value
    return table

def create_docx_report(screenerdf, doc_name, daily_id, wkly_id, clean_image=False):
    """Download charts for filtered stocks and create document."""
    doc = docx.Document()
    file_name = f'{doc_name}.docx'

    for _, row in screenerdf.iterrows():
        xyz = row['Symbol']
        base_url_daily = f'https://stockcharts.com/c-sc/sc?s=ticker&p=D&yr=0&mn=6&dy=0&i={daily_id}&r=tdy'
        daily_url = base_url_daily.replace('tdy', str(time.time())).replace('ticker', xyz)
        daily_image_path = f'src/data/images/{xyz}.png'
        if download_image(daily_url, daily_image_path):
            add_image_to_doc(doc, daily_image_path, clean_image)

        base_url_weekly = f'https://stockcharts.com/c-sc/sc?s=ticker&p=W&yr=2&mn=6&dy=0&i={wkly_id}&r=tdy'
        weekly_url = base_url_weekly.replace('tdy', str(time.time())).replace('ticker', xyz)
        weekly_image_path = f'src/data/images/{xyz}_week.png'
        if download_image(weekly_url, weekly_image_path):
            add_image_to_doc(doc, weekly_image_path, clean_image)

        create_vertical_table(
            doc,
            row['Sector'],
            row['Industry'],
            row['Sub-Industry'],
            row['Forward EPS Long Term Growth (3-5 Yrs)'],
            row['EPS Growth (Last Qtr vs. Same Qtr Prior Yr)'],
            row['EPS Growth (TTM vs Prior TTM)'],
            row['EPS Growth (3 Year History)'],
            row['EPS Growth (5 Year Historical)'],
            row['EPS Growth (Proj Next Yr vs. This Yr)'],
            row['Revenue Growth (TTM vs. Prior TTM)'],
            row['Revenue Growth (Last Qtr vs. Same Qtr Prior Yr)'],
            row['Equity Summary Score (ESS) from LSEG StarMine'],
        )

        p = doc.add_paragraph()
        add_hyperlink(p, f"http://127.0.0.1:{LIKE_SERVER_PORT}/like?t={xyz}", f"Like {xyz}")
        doc.add_paragraph("")
        print("Processed chart for stock:", xyz)

    p = doc.add_paragraph()
    p.add_run("Tip: Click 'Like TICKER' links above; liked tickers are saved to ")
    p.add_run(LIKES_OUTPUT_PATH)
    doc.save(file_name)
    os.startfile(file_name)
