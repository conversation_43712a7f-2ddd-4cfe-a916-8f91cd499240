# Cookie Management for Fidelity Portfolio Alerter

## Overview

The Fidelity Portfolio Alerter has been enhanced with robust cookie management features to handle authentication expiration gracefully. When your Fidelity session expires, the application will now:

1. **Detect cookie expiration** automatically
2. **Send desktop notifications** alerting you to the issue
3. **Provide interactive tools** to refresh your authentication
4. **Resume monitoring** seamlessly after cookie update

## New Features

### 🍪 Automatic Cookie Expiration Detection
- Detects HTTP 401/403 responses indicating expired authentication
- Distinguishes between network errors and authentication failures
- Provides clear error messages and guidance

### 📢 Enhanced Notifications
- Desktop notifications for cookie expiration
- Urgent notifications with longer display time
- Clear instructions on how to resolve the issue

### 🔧 Command Line Tools

#### Test Cookie Validity
```bash
python src/fidelity/portfolio_alerter.py --test-cookie
```
Checks if your current cookie is still valid without starting the monitoring loop.

#### Interactive Cookie Refresh
```bash
python src/fidelity/portfolio_alerter.py --refresh-cookie
```
Prompts you to enter a new cookie and updates the .env file automatically.

#### Direct Cookie Update
```bash
python src/fidelity/portfolio_alerter.py --update-cookie "your_new_cookie_here"
```
Updates the cookie directly from the command line.

### 🔄 Automatic Recovery
When running in monitoring mode, if a cookie expires:
1. The application pauses monitoring
2. Sends a desktop notification
3. Prompts for a new cookie interactively
4. Tests the new cookie validity
5. Resumes monitoring if successful

## How to Get a New Cookie

When your cookie expires, follow these steps:

1. **Open your browser** and log into Fidelity
2. **Navigate to Portfolio > Positions**
3. **Open Developer Tools** (Press F12)
4. **Go to the Network tab** and refresh the page
5. **Find a request** to the 'positions' API
6. **Copy the entire 'Cookie' header** value
7. **Use one of the refresh methods** described above

## Usage Examples

### Basic Monitoring (with automatic validation)
```bash
python src/fidelity/portfolio_alerter.py
```
Now includes automatic cookie validation before starting.

### Reset Peak Profit (with error handling)
```bash
python src/fidelity/portfolio_alerter.py --reset
```
Handles cookie expiration gracefully during reset operations.

### Test Your Setup
```bash
python test_cookie_management.py
```
Runs a comprehensive test of all cookie management features.

## Error Handling Improvements

### Network Resilience
- Distinguishes between temporary network issues and authentication problems
- Implements exponential backoff for repeated failures
- Provides clear feedback on connection status

### User-Friendly Messages
- Uses emojis and clear formatting for better readability
- Provides actionable guidance for each type of error
- Includes timestamps for better debugging

### Graceful Degradation
- Continues monitoring after temporary failures
- Maintains state across authentication refreshes
- Preserves peak profit tracking during interruptions

## Security Notes

- Cookies are handled securely and not logged
- Input is hidden when entering new cookies via command line
- .env file permissions should be restricted to your user account

## Troubleshooting

### "Cookie doesn't look valid" Warning
If you see this warning, double-check that you copied the entire Cookie header value, including all the session tokens.

### "Failed to update cookie in .env file"
Check that the .env file exists and is writable by your user account.

### Persistent Authentication Failures
Try logging out of Fidelity completely, clearing browser cookies, and logging back in to get a fresh session.
