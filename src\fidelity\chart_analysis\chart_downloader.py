# chart_downloader.py - Main orchestrator for chart downloading and UI
import aiohttp

from .analysis import filter_stocks, save_filtered_screener
from .docx_generator import create_docx_report
from . import web_ui # To modify globals like CURRENT_TICKERS

async def downloadChartsFidelityV2(screenerdf, doc_name=None):
    """
    Main function that coordinates filtering and chart download OR just launches the UI pipeline.
    """
    async with aiohttp.ClientSession() as session:
        # Step 1: Filter stocks
        filtered_df, excluded_tickers = await filter_stocks(session, screenerdf)

    # Step 2: Print excluded tickers
    if excluded_tickers:
        for ticker, reason in excluded_tickers:
            print(f"Excluded {ticker} for reasons: {reason}")

    # Step 3: Save filtered screener
    sorted_df = save_filtered_screener(filtered_df, excluded_tickers)

    # Pipeline data for UI
    # web_ui.CURRENT_TICKERS = sorted_df.to_dict(orient='records')

    # daily_id = input("Enter daily id: ")
    # wkly_id = input("Enter weekly id: ")

    # web_ui.DAILY_CHART_ID = daily_id
    # web_ui.WEEKLY_CHART_ID = wkly_id

    # Start servers and open browser
    # try:
    #     start_like_server()
    #     ui_url = f"http://127.0.0.1:{LIKE_SERVER_PORT+1}/app"
    #     print(f"UI at {ui_url}")
    #     webbrowser.open(ui_url)
    # except Exception as e:
    #     print(f"Warning: Could not start UI server: {e}")
    print("Use ui_only.bat to launch the UI.")

    # If DOCX requested, proceed to build it too (optional)
    if doc_name:
        clean_image_input = input("Clean images after insert? (Y/N): ").strip().lower()
        clean_image = clean_image_input == 'y'
        create_docx_report(sorted_df, doc_name, input("Enter daily id: "), input("Enter weekly id: "), clean_image)
        try:
            input("Leave this window open. After clicking Like links in Word, press Enter here to finish... ")
        except Exception:
            pass
