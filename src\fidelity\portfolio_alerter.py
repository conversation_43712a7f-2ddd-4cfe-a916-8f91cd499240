import sys
import os
import time
import json
import argparse
from decimal import Decimal, InvalidOperation
from dotenv import load_dotenv
import requests
from plyer import notification

# Add project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.fidelity.common.api_headers import get_positions_headers

# Constants
STATE_FILE = 'portfolio_state.json'
API_URL = 'https://digital.fidelity.com/ftgw/digital/positions/poswebex/api/positions'

def get_env_variables():
    """Load required environment variables."""
    load_dotenv()
    cookie = os.getenv('FIDELITY_POSITIONS_COOKIE')
    account_number = os.getenv('FIDELITY_ACCOUNT_NUMBER')
    if not cookie:
        raise ValueError("FIDELITY_POSITIONS_COOKIE not found in .env file")
    if not account_number:
        raise ValueError("FIDELITY_ACCOUNT_NUMBER not found in .env file. Please add it.")
    return cookie, account_number

def fetch_portfolio_data(cookie, account_number):
    """Fetch portfolio data from the Fidelity API using a POST request."""
    headers = get_positions_headers(cookie)

    # This payload requests the unadjusted gain/loss values.
    payload = {"accts":[account_number],"amc":"H4sIAAAAAAAAA+VVXU/iQBT9K2Sea1K0rS5vFXQhMUigMfsRH4bObblhmGnmAxcJ/90Z6Va24IuJ8WHf2nPP/cg5p+mW0DyXVhhNer+3RFYGpUhLBbACYUiP/CSBp5i+AuprA2qA9LrxZZJE8XkYBn8HjJhj/wqjOIqvwth1CcyXY7pybDISDNfILOWds052P3DVHN30gnIN7pnqRbqf0mAcxRLYtZJLULSEdjmXnEMJI7EGbfypmbLatFlGUYaiTDmXT+DuM8o6dH40dI9TISyaTXsIgwIFsL4URuHcvgrUooCWVRtDPQWDqtbxgDjhVDRAoeTq1nL+gPDUgApK1O52vyrbVE7BuBuQSipTSI4yZWvUUm1moNaYg25vrg15X3vv534uaQSu4Zmdn6gUhWwvKRTS8mizNQup8BmYq7zBGg0cxcO74FljuyI94SRoDJgoyezJC11Qbid3N7x0+Ni9uwjQfDOUVsNQcmeRNtdUo+5L5ltT4m2o5XoNSHaX/nOZoX8yH2xvUt2UpT9uBmQXbE/FPkm+hWFy1Y79edSNLi/CuBX76X027Iym6f8X9/2GL0j7geRflvLDNHwk5Z+R6nTwkH0nu8eALKieTWYNt3oW7pPy7f4P8Lh7AWqzHbYRBgAA","isRefresh":False,"settings":{"groupBy":"0","filter":"","gbsMsgSeenDates":"Jul-12-2025,Aug-13-2025","isUnadjusted":True,"isShowForeignStocks":False,"showBasketMsg":True,"showTodaysGLResetMsg":True,"showGbsMsg":True,"isPriorDayGL":False,"additionalFilter":""},"isBasketEnable":False,"isGroupBySecurityEnable":False,"toggles":{"ap126216_PWE_Symbol_Total_Row":True,"ap126216_PWE_Baskets_Row_Message":False,"ap126216_PWE_TodaysGL_Reset_Settings_Message":False,"ap126216_PWE_GbS_Account_Row_Msg":True,"ap126216_PWE_PriorDayGL_Setting":True,"ap126216_PWE_Additional_Filters":True,"ap126216_PWE_Alt_Inv_Cost_Basis_Info":False,"ap126216_PWE_Custom_Columns":False}}

    try:
        response = requests.post(API_URL, headers=headers, json=payload)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching data from Fidelity: {e}")
        return None

def calculate_total_profit(data):
    """
    Calculate the total UNADJUSTED profit from today's gain/loss for stock positions.
    """
    total_profit = Decimal('0.0')
    if not data or 'rowData' not in data:
        print("Warning: 'rowData' not found in the API response.")
        return total_profit

    for position in data['rowData']:
        # We only care about rows that are actual positions and are equities.
        if position.get('rowType') != 'POSITION' or position.get('meta', {}).get('securityType') != 'Equity':
            continue

        try:
            # Extract the numeric value for total gain/loss.
            # It's nested inside: totGLStk -> top -> val
            gain_loss_value = position.get('totGLStk', {}).get('top', {}).get('val')

            # The value can be null or missing for some positions, so we check it.
            if gain_loss_value is not None:
                # The value is already a number in the JSON, but we cast to Decimal for precision.
                total_profit += Decimal(str(gain_loss_value))

        except (InvalidOperation, TypeError) as e:
            # This will catch errors if the value is not a valid number.
            symbol = position.get('sym', {}).get('name', 'Unknown')
            print(f"Could not parse gain/loss for symbol '{symbol}'. Error: {e}")
            continue

    return total_profit

def load_state():
    """Load the peak profit from the state file."""
    if not os.path.exists(STATE_FILE):
        return {'peak_profit': Decimal('-Infinity')}
    try:
        with open(STATE_FILE, 'r') as f:
            state = json.load(f)
            state['peak_profit'] = Decimal(state.get('peak_profit', '-Infinity'))
            return state
    except (json.JSONDecodeError, FileNotFoundError):
        return {'peak_profit': Decimal('-Infinity')}

def save_state(state):
    """Save the peak profit to the state file."""
    # Convert Decimal to string for JSON serialization
    state_to_save = {'peak_profit': str(state['peak_profit'])}
    with open(STATE_FILE, 'w') as f:
        json.dump(state_to_save, f, indent=4)

def send_notification(title, message):
    """Send a desktop notification."""
    try:
        notification.notify(
            title=title,
            message=message,
            app_name='Fidelity Alerter',
            timeout=10  # Notification will disappear after 10 seconds
        )
        print(f"Notification sent: '{title}'")
    except Exception as e:
        print(f"Failed to send notification: {e}")

def main():
    """Main function to run the portfolio alerter."""
    parser = argparse.ArgumentParser(description="Fidelity Portfolio Alerter")
    parser.add_argument('--reset', action='store_true', help='Reset the peak profit to the current profit.')
    args = parser.parse_args()

    try:
        cookie, account_number = get_env_variables()
    except ValueError as e:
        print(e)
        sys.exit(1)

    state = load_state()

    if args.reset:
        print("Resetting peak profit...")
        data = fetch_portfolio_data(cookie, account_number)
        if data:
            current_profit = calculate_total_profit(data)
            state['peak_profit'] = current_profit
            save_state(state)
            print(f"Peak profit has been reset to: ${current_profit:.2f}")
        else:
            print("Could not fetch data to reset peak profit.")
        # Exit after resetting
        sys.exit(0)

    print("Starting Fidelity Portfolio Alerter... Press Ctrl+C to stop.")

    while True:
        data = fetch_portfolio_data(cookie, account_number)
        if not data:
            time.sleep(300)  # Wait 5 minutes before retrying
            continue

        current_profit = calculate_total_profit(data)
        peak_profit = state.get('peak_profit', Decimal('-Infinity'))

        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] Current Profit: ${current_profit:.2f}, Peak Profit: ${peak_profit:.2f}")

        # Update peak profit if a new high is reached
        if current_profit > peak_profit:
            state['peak_profit'] = current_profit
            save_state(state)
            print(f"New peak profit reached: ${current_profit:.2f}")
            peak_profit = current_profit # Update for the current iteration's check

        # Check for profit drop alert
        if peak_profit > 0: # Only alert if we have a positive peak profit
            threshold = peak_profit * Decimal('0.5')
            if current_profit < threshold:
                message = (f"Current profit ${current_profit:.2f} is below 50% "
                           f"of peak profit ${peak_profit:.2f} (Threshold: ${threshold:.2f})")
                send_notification("Fidelity Profit Alert!", message)

        time.sleep(30)  # Wait for 5 minutes

if __name__ == "__main__":
    main()
