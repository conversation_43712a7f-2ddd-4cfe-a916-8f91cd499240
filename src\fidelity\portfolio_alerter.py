import sys
import os
import time
import json
import argparse
import getpass
from decimal import Decimal, InvalidOperation
from dotenv import load_dotenv, set_key
import requests
from plyer import notification

# Add project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.fidelity.common.api_headers import get_positions_headers

# Constants
# Make state file path relative to the script directory, not current working directory
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.abspath(os.path.join(SCRIPT_DIR, '..', '..'))
STATE_FILE = os.path.join(PROJECT_ROOT, 'portfolio_state.json')
API_URL = 'https://digital.fidelity.com/ftgw/digital/positions/poswebex/api/positions'
ENV_FILE = os.path.join(PROJECT_ROOT, '.env')

class CookieExpiredError(Exception):
    """Raised when the authentication cookie has expired."""
    pass

def get_env_variables():
    """Load required environment variables."""
    load_dotenv()
    cookie = os.getenv('FIDELITY_POSITIONS_COOKIE')
    account_number = os.getenv('FIDELITY_ACCOUNT_NUMBER')
    if not cookie:
        raise ValueError("FIDELITY_POSITIONS_COOKIE not found in .env file")
    if not account_number:
        raise ValueError("FIDELITY_ACCOUNT_NUMBER not found in .env file. Please add it.")
    return cookie, account_number

def update_cookie_in_env(new_cookie):
    """Update the cookie in the .env file."""
    try:
        set_key(ENV_FILE, 'FIDELITY_POSITIONS_COOKIE', new_cookie)
        print(f"Cookie updated successfully in .env file: {ENV_FILE}")
        return True
    except Exception as e:
        print(f"Failed to update cookie in .env file ({ENV_FILE}): {e}")
        return False

def prompt_for_new_cookie():
    """Prompt user for a new cookie via command line."""
    print("\n" + "="*60)
    print("🍪 COOKIE EXPIRED - ACTION REQUIRED")
    print("="*60)
    print("Your Fidelity authentication cookie has expired.")
    print("Please follow these steps to get a new cookie:")
    print("\n1. Open your browser and log into Fidelity")
    print("2. Navigate to Portfolio > Positions")
    print("3. Open Developer Tools (F12)")
    print("4. Go to Network tab and refresh the page")
    print("5. Find a request to 'positions' API")
    print("6. Copy the entire 'Cookie' header value")
    print("\nPaste the new cookie below (input will be hidden):")

    try:
        new_cookie = getpass.getpass("New Cookie: ").strip()
        if not new_cookie:
            print("No cookie provided. Exiting...")
            return None

        # Basic validation - check if it looks like a cookie
        if 'RtAzC=' not in new_cookie or len(new_cookie) < 100:
            print("Warning: The provided cookie doesn't look valid.")
            confirm = input("Continue anyway? (y/N): ").strip().lower()
            if confirm != 'y':
                return None

        return new_cookie
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        return None

def test_cookie_validity(cookie, account_number):
    """Test if the cookie is still valid by making a test API call."""
    try:
        # Use the same payload as the main fetch function for consistency
        headers = get_positions_headers(cookie)
        payload = {"accts":[account_number],"amc":"H4sIAAAAAAAAA+VVXU/iQBT9K2Sea1K0rS5vFXQhMUigMfsRH4bObblhmGnmAxcJ/90Z6Va24IuJ8WHf2nPP/cg5p+mW0DyXVhhNer+3RFYGpUhLBbACYUiP/CSBp5i+AuprA2qA9LrxZZJE8XkYBn8HjJhj/wqjOIqvwth1CcyXY7pybDISDNfILOWds052P3DVHN30gnIN7pnqRbqf0mAcxRLYtZJLULSEdjmXnEMJI7EGbfypmbLatFlGUYaiTDmXT+DuM8o6dH40dI9TISyaTXsIgwIFsL4URuHcvgrUooCWVRtDPQWDqtbxgDjhVDRAoeTq1nL+gPDUgApK1O52vyrbVE7BuBuQSipTSI4yZWvUUm1moNaYg25vrg15X3vv534uaQSu4Zmdn6gUhWwvKRTS8mizNQup8BmYq7zBGg0cxcO74FljuyI94SRoDJgoyezJC11Qbid3N7x0+Ni9uwjQfDOUVsNQcmeRNtdUo+5L5ltT4m2o5XoNSHaX/nOZoX8yH2xvUt2UpT9uBmQXbE/FPkm+hWFy1Y79edSNLi/CuBX76X027Iym6f8X9/2GL0j7geRflvLDNHwk5Z+R6nTwkH0nu8eALKieTWYNt3oW7pPy7f4P8Lh7AWqzHbYRBgAA","isRefresh":False,"settings":{"groupBy":"0","filter":"","gbsMsgSeenDates":"Jul-12-2025,Aug-13-2025","isUnadjusted":True,"isShowForeignStocks":False,"showBasketMsg":True,"showTodaysGLResetMsg":True,"showGbsMsg":True,"isPriorDayGL":False,"additionalFilter":""},"isBasketEnable":False,"isGroupBySecurityEnable":False,"toggles":{"ap126216_PWE_Symbol_Total_Row":True,"ap126216_PWE_Baskets_Row_Message":False,"ap126216_PWE_TodaysGL_Reset_Settings_Message":False,"ap126216_PWE_GbS_Account_Row_Msg":True,"ap126216_PWE_PriorDayGL_Setting":True,"ap126216_PWE_Additional_Filters":True,"ap126216_PWE_Alt_Inv_Cost_Basis_Info":False,"ap126216_PWE_Custom_Columns":False}}

        response = requests.post(API_URL, headers=headers, json=payload, timeout=10)

        if response.status_code == 401:
            return False, "Authentication failed - cookie expired"
        elif response.status_code == 403:
            return False, "Access forbidden - check account permissions"
        elif response.status_code == 200:
            # Additional check: make sure we got valid JSON response
            try:
                data = response.json()
                if 'rowData' in data or 'error' not in data:
                    return True, "Cookie is valid and working"
                else:
                    return False, "Cookie valid but API returned unexpected data"
            except ValueError:
                return False, "Cookie valid but response is not valid JSON"
        elif response.status_code >= 400:
            return False, f"HTTP {response.status_code} - {response.reason}"
        else:
            return True, "Cookie appears to be valid"

    except requests.exceptions.RequestException as e:
        return False, f"Network error: {e}"

def fetch_portfolio_data(cookie, account_number):
    """Fetch portfolio data from the Fidelity API using a POST request."""
    headers = get_positions_headers(cookie)

    # This payload requests the unadjusted gain/loss values.
    payload = {"accts":[account_number],"amc":"H4sIAAAAAAAAA+VVXU/iQBT9K2Sea1K0rS5vFXQhMUigMfsRH4bObblhmGnmAxcJ/90Z6Va24IuJ8WHf2nPP/cg5p+mW0DyXVhhNer+3RFYGpUhLBbACYUiP/CSBp5i+AuprA2qA9LrxZZJE8XkYBn8HjJhj/wqjOIqvwth1CcyXY7pybDISDNfILOWds052P3DVHN30gnIN7pnqRbqf0mAcxRLYtZJLULSEdjmXnEMJI7EGbfypmbLatFlGUYaiTDmXT+DuM8o6dH40dI9TISyaTXsIgwIFsL4URuHcvgrUooCWVRtDPQWDqtbxgDjhVDRAoeTq1nL+gPDUgApK1O52vyrbVE7BuBuQSipTSI4yZWvUUm1moNaYg25vrg15X3vv534uaQSu4Zmdn6gUhWwvKRTS8mizNQup8BmYq7zBGg0cxcO74FljuyI94SRoDJgoyezJC11Qbid3N7x0+Ni9uwjQfDOUVsNQcmeRNtdUo+5L5ltT4m2o5XoNSHaX/nOZoX8yH2xvUt2UpT9uBmQXbE/FPkm+hWFy1Y79edSNLi/CuBX76X027Iym6f8X9/2GL0j7geRflvLDNHwk5Z+R6nTwkH0nu8eALKieTWYNt3oW7pPy7f4P8Lh7AWqzHbYRBgAA","isRefresh":False,"settings":{"groupBy":"0","filter":"","gbsMsgSeenDates":"Jul-12-2025,Aug-13-2025","isUnadjusted":True,"isShowForeignStocks":False,"showBasketMsg":True,"showTodaysGLResetMsg":True,"showGbsMsg":True,"isPriorDayGL":False,"additionalFilter":""},"isBasketEnable":False,"isGroupBySecurityEnable":False,"toggles":{"ap126216_PWE_Symbol_Total_Row":True,"ap126216_PWE_Baskets_Row_Message":False,"ap126216_PWE_TodaysGL_Reset_Settings_Message":False,"ap126216_PWE_GbS_Account_Row_Msg":True,"ap126216_PWE_PriorDayGL_Setting":True,"ap126216_PWE_Additional_Filters":True,"ap126216_PWE_Alt_Inv_Cost_Basis_Info":False,"ap126216_PWE_Custom_Columns":False}}

    try:
        response = requests.post(API_URL, headers=headers, json=payload, timeout=30)

        # Check for authentication errors
        if response.status_code == 401:
            raise CookieExpiredError("Authentication failed - cookie has expired")
        elif response.status_code == 403:
            raise CookieExpiredError("Access forbidden - check account permissions")

        response.raise_for_status()
        return response.json()

    except CookieExpiredError:
        # Re-raise cookie errors to be handled by caller
        raise
    except requests.exceptions.Timeout:
        print("⚠️  Request timeout - Fidelity servers may be slow")
        return None
    except requests.exceptions.ConnectionError:
        print("⚠️  Connection error - check your internet connection")
        return None
    except requests.exceptions.RequestException as e:
        print(f"⚠️  Error fetching data from Fidelity: {e}")
        return None

def calculate_total_profit(data):
    """
    Calculate the total UNADJUSTED profit from today's gain/loss for stock positions.
    """
    total_profit = Decimal('0.0')
    if not data or 'rowData' not in data:
        print("Warning: 'rowData' not found in the API response.")
        return total_profit

    for position in data['rowData']:
        # We only care about rows that are actual positions and are equities.
        if position.get('rowType') != 'POSITION' or position.get('meta', {}).get('securityType') != 'Equity':
            continue

        try:
            # Extract the numeric value for total gain/loss.
            # It's nested inside: totGLStk -> top -> val
            gain_loss_value = position.get('totGLStk', {}).get('top', {}).get('val')

            # The value can be null or missing for some positions, so we check it.
            if gain_loss_value is not None:
                # The value is already a number in the JSON, but we cast to Decimal for precision.
                total_profit += Decimal(str(gain_loss_value))

        except (InvalidOperation, TypeError) as e:
            # This will catch errors if the value is not a valid number.
            symbol = position.get('sym', {}).get('name', 'Unknown')
            print(f"Could not parse gain/loss for symbol '{symbol}'. Error: {e}")
            continue

    return total_profit

def find_existing_state_files():
    """Find any existing portfolio_state.json files in common locations."""
    possible_locations = [
        'portfolio_state.json',  # Current directory
        os.path.join(os.getcwd(), 'portfolio_state.json'),  # Current working directory
        os.path.join(SCRIPT_DIR, 'portfolio_state.json'),  # Script directory
        os.path.join(PROJECT_ROOT, 'portfolio_state.json'),  # Project root (new location)
    ]

    found_files = []
    for location in possible_locations:
        if os.path.exists(location) and location not in found_files:
            try:
                with open(location, 'r') as f:
                    data = json.load(f)
                    if 'peak_profit' in data:
                        found_files.append((location, data))
            except (json.JSONDecodeError, FileNotFoundError):
                continue

    return found_files

def load_state():
    """Load the peak profit from the state file."""
    if not os.path.exists(STATE_FILE):
        # Check if there are any existing state files we can migrate
        existing_files = find_existing_state_files()
        if existing_files:
            print(f"📁 Found existing state file(s):")
            for i, (location, data) in enumerate(existing_files):
                peak_profit = data.get('peak_profit', 'Unknown')
                print(f"   {i+1}. {location} (Peak Profit: ${peak_profit})")

            if len(existing_files) == 1:
                # Auto-migrate if only one file found
                location, data = existing_files[0]
                print(f"🔄 Auto-migrating state from: {location}")
                save_state(data)
                return {'peak_profit': Decimal(str(data.get('peak_profit', '-Infinity')))}
            else:
                print("💡 Multiple state files found. Use --migrate-state to choose which one to use.")

        return {'peak_profit': Decimal('-Infinity')}

    try:
        with open(STATE_FILE, 'r') as f:
            state = json.load(f)
            state['peak_profit'] = Decimal(state.get('peak_profit', '-Infinity'))
            return state
    except (json.JSONDecodeError, FileNotFoundError):
        return {'peak_profit': Decimal('-Infinity')}

def save_state(state):
    """Save the peak profit to the state file."""
    # Convert Decimal to string for JSON serialization
    state_to_save = {'peak_profit': str(state['peak_profit'])}
    with open(STATE_FILE, 'w') as f:
        json.dump(state_to_save, f, indent=4)

def send_notification(title, message, urgent=False):
    """Send a desktop notification."""
    try:
        timeout = 30 if urgent else 10  # Longer timeout for urgent notifications
        notification.notify(
            title=title,
            message=message,
            app_name='Fidelity Alerter',
            timeout=timeout
        )
        print(f"📢 Notification sent: '{title}'")
    except Exception as e:
        print(f"⚠️  Failed to send notification: {e}")

def send_cookie_expired_alert():
    """Send a specific alert for cookie expiration."""
    title = "🍪 Fidelity Cookie Expired!"
    message = ("Your authentication has expired.\n"
               "Run: python portfolio_alerter.py --refresh-cookie\n"
               "to update your credentials.")
    send_notification(title, message, urgent=True)
    print("\n" + "="*60)
    print("🚨 URGENT: AUTHENTICATION EXPIRED")
    print("="*60)
    print("Your Fidelity cookie has expired.")
    print("Portfolio monitoring has been paused.")
    print("\nTo resume monitoring:")
    print("1. Run: python portfolio_alerter.py --refresh-cookie")
    print("2. Or restart with: python portfolio_alerter.py")
    print("="*60)

def main():
    """Main function to run the portfolio alerter."""
    parser = argparse.ArgumentParser(description="Fidelity Portfolio Alerter")
    parser.add_argument('--reset', action='store_true', help='Reset the peak profit to the current profit.')
    parser.add_argument('--test-cookie', action='store_true', help='Test if the current cookie is valid.')
    parser.add_argument('--update-cookie', type=str, help='Update the cookie in .env file with the provided value.')
    parser.add_argument('--refresh-cookie', action='store_true', help='Interactively refresh the expired cookie.')
    parser.add_argument('--find-state', action='store_true', help='Find existing portfolio state files.')
    parser.add_argument('--migrate-state', type=str, help='Migrate state from specified file path.')
    args = parser.parse_args()

    try:
        cookie, account_number = get_env_variables()
    except ValueError as e:
        print(f"❌ {e}")
        sys.exit(1)

    # Handle cookie testing
    if args.test_cookie:
        print("🧪 Testing cookie validity...")
        is_valid, message = test_cookie_validity(cookie, account_number)
        if is_valid:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
        sys.exit(0 if is_valid else 1)

    # Handle cookie update from command line
    if args.update_cookie:
        print("🔄 Updating cookie...")
        if update_cookie_in_env(args.update_cookie):
            print("✅ Cookie updated successfully. Please restart the application.")
        else:
            print("❌ Failed to update cookie.")
        sys.exit(0)

    # Handle interactive cookie refresh
    if args.refresh_cookie:
        new_cookie = prompt_for_new_cookie()
        if new_cookie and update_cookie_in_env(new_cookie):
            print("✅ Cookie refreshed successfully. Please restart the application.")
        else:
            print("❌ Cookie refresh failed or cancelled.")
        sys.exit(0)

    # Handle finding existing state files
    if args.find_state:
        print("🔍 Searching for existing portfolio state files...")
        existing_files = find_existing_state_files()
        if existing_files:
            print(f"📁 Found {len(existing_files)} state file(s):")
            for i, (location, data) in enumerate(existing_files):
                peak_profit = data.get('peak_profit', 'Unknown')
                print(f"   {i+1}. {location}")
                print(f"      Peak Profit: ${peak_profit}")
                print(f"      Last Modified: {time.ctime(os.path.getmtime(location))}")
            print(f"\n💡 To migrate a specific file, use:")
            print(f"   python {sys.argv[0]} --migrate-state <file_path>")
        else:
            print("❌ No existing state files found.")
        sys.exit(0)

    # Handle state migration
    if args.migrate_state:
        source_file = args.migrate_state
        if not os.path.exists(source_file):
            print(f"❌ Source file not found: {source_file}")
            sys.exit(1)

        try:
            with open(source_file, 'r') as f:
                data = json.load(f)

            if 'peak_profit' not in data:
                print(f"❌ Invalid state file: {source_file} (missing peak_profit)")
                sys.exit(1)

            # Save to new location
            save_state(data)
            peak_profit = data.get('peak_profit', 'Unknown')
            print(f"✅ State migrated successfully!")
            print(f"   From: {source_file}")
            print(f"   To: {STATE_FILE}")
            print(f"   Peak Profit: ${peak_profit}")

        except (json.JSONDecodeError, FileNotFoundError) as e:
            print(f"❌ Failed to migrate state: {e}")
            sys.exit(1)

        sys.exit(0)

    # Test cookie validity before starting
    print("🔍 Validating authentication...")
    is_valid, message = test_cookie_validity(cookie, account_number)
    if not is_valid:
        print(f"❌ {message}")
        print("\n💡 Use --refresh-cookie to update your authentication")
        sys.exit(1)
    print("✅ Authentication validated successfully")

    state = load_state()

    if args.reset:
        print("🔄 Resetting peak profit...")
        try:
            data = fetch_portfolio_data(cookie, account_number)
            if data:
                current_profit = calculate_total_profit(data)
                state['peak_profit'] = current_profit
                save_state(state)
                print(f"✅ Peak profit has been reset to: ${current_profit:.2f}")
            else:
                print("❌ Could not fetch data to reset peak profit.")
        except CookieExpiredError as e:
            print(f"❌ {e}")
            print("💡 Use --refresh-cookie to update your authentication")
        # Exit after resetting
        sys.exit(0)

    print("🚀 Starting Fidelity Portfolio Alerter... Press Ctrl+C to stop.")
    print("📊 Monitoring portfolio for profit changes...")

    consecutive_failures = 0
    max_failures = 3

    while True:
        try:
            data = fetch_portfolio_data(cookie, account_number)
            if not data:
                consecutive_failures += 1
                if consecutive_failures >= max_failures:
                    print(f"⚠️  {consecutive_failures} consecutive failures. Waiting longer before retry...")
                    time.sleep(600)  # Wait 10 minutes after multiple failures
                else:
                    time.sleep(300)  # Wait 5 minutes before retrying
                continue

            # Reset failure counter on success
            consecutive_failures = 0

            current_profit = calculate_total_profit(data)
            peak_profit = state.get('peak_profit', Decimal('-Infinity'))

            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] Current Profit: ${current_profit:.2f}, Peak Profit: ${peak_profit:.2f}")

            # Update peak profit if a new high is reached
            if current_profit > peak_profit:
                state['peak_profit'] = current_profit
                save_state(state)
                print(f"🎉 New peak profit reached: ${current_profit:.2f}")
                peak_profit = current_profit # Update for the current iteration's check

            # Check for profit drop alert
            if peak_profit > 0: # Only alert if we have a positive peak profit
                threshold = peak_profit * Decimal('0.5')
                if current_profit < threshold:
                    message = (f"Current profit ${current_profit:.2f} is below 50% "
                               f"of peak profit ${peak_profit:.2f} (Threshold: ${threshold:.2f})")
                    send_notification("Fidelity Profit Alert!", message)

            time.sleep(30)  # Wait 30 seconds

        except CookieExpiredError as e:
            print(f"\n❌ {e}")
            send_cookie_expired_alert()

            print("\n🔄 Attempting to refresh cookie...")
            new_cookie = prompt_for_new_cookie()

            if new_cookie:
                if update_cookie_in_env(new_cookie):
                    print("✅ Cookie updated successfully. Testing new cookie...")
                    # Reload environment variables
                    load_dotenv()
                    cookie = os.getenv('FIDELITY_POSITIONS_COOKIE')

                    # Test the new cookie
                    is_valid, message = test_cookie_validity(cookie, account_number)
                    if is_valid:
                        print("✅ New cookie is valid. Resuming monitoring...")
                        consecutive_failures = 0
                        continue
                    else:
                        print(f"❌ New cookie is invalid: {message}")
                else:
                    print("❌ Failed to update cookie in .env file.")

            print("💡 Please restart the application with a valid cookie.")
            print("   Use: python portfolio_alerter.py --refresh-cookie")
            sys.exit(1)

        except KeyboardInterrupt:
            print("\n👋 Portfolio monitoring stopped by user.")
            sys.exit(0)
        except Exception as e:
            print(f"⚠️  Unexpected error: {e}")
            consecutive_failures += 1
            time.sleep(60)  # Wait 1 minute before retrying

if __name__ == "__main__":
    main()
