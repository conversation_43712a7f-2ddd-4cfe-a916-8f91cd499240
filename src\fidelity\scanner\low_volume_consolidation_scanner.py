# low_volume_consolidation_scanner.py - Identifies stocks with very low relative volume for consolidation analysis

import requests
import json
import pandas as pd
from typing import List, Dict, Tuple
from datetime import datetime
import os
import glob
import argparse
from dotenv import load_dotenv

# Import existing functions
from fidelity.common.api_headers import get_fidelity_headers_v2
from fidelity.scanner.volume_scanner import get_csv_stock_list_from_excel, chunk_list

load_dotenv()

def analyze_low_volume_stocks(response_data: Dict, max_relative_volume: float = 0.5) -> List[Tuple]:
    """
    Analyze stocks for very low relative volume indicating consolidation patterns.

    Args:
        response_data: API response containing quote data
        max_relative_volume: Maximum relative volume ratio (default 0.5 = 50% of average)

    Returns:
        List of tuples (symbol, relative_volume_ratio, price, pct_change, volume, avg_volume)
    """
    consolidation_candidates = []

    # Debug: Check response structure
    if 'quoteData' not in response_data:
        print(f"❌ Error: 'quoteData' key not found in response. Available keys: {list(response_data.keys())}")
        return consolidation_candidates

    quote_data = response_data['quoteData']
    if not isinstance(quote_data, list):
        print(f"❌ Error: 'quoteData' is not a list. Type: {type(quote_data)}")
        return consolidation_candidates

    for i, data in enumerate(quote_data):
        try:
            # Debug: Check if data is a dictionary
            if not isinstance(data, dict):
                print(f"❌ Error: Item {i} in quoteData is not a dictionary. Type: {type(data)}, Value: {data}")
                continue

            # Check for required fields
            required_fields = ['symbol', 'volume', 'avgVol10Day']
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                print(f"❌ Error: Missing required fields {missing_fields} for item {i}. Available fields: {list(data.keys())}")
                continue

            symbol = data['symbol']

            # Handle volume data - check if it's already a number or needs string processing
            volume_raw = data['volume']
            if isinstance(volume_raw, str):
                volume = float(volume_raw.replace(',', ''))
            else:
                volume = float(volume_raw)

            avgVol10Day_raw = data['avgVol10Day']
            if isinstance(avgVol10Day_raw, str):
                avgVol10Day = float(avgVol10Day_raw.replace(',', ''))
            else:
                avgVol10Day = float(avgVol10Day_raw)

            lastPrice = data.get('lastPrice', 'N/A')
            pctChgToday = data.get('pctChgToday', 'N/A')

            # Skip if no volume data available
            if avgVol10Day == 0:
                continue

            relative_volume = volume / avgVol10Day

            # Filter for stocks with low relative volume (consolidation candidates)
            if relative_volume <= max_relative_volume:
                consolidation_candidates.append((
                    symbol,
                    relative_volume,
                    lastPrice,
                    pctChgToday,
                    volume,
                    avgVol10Day
                ))

        except (ValueError, KeyError, TypeError) as e:
            symbol_info = data.get('symbol', 'Unknown') if isinstance(data, dict) else f"Item {i}"
            print(f"❌ Error processing data for {symbol_info}: {e}")
            print(f"   Data type: {type(data)}")
            if isinstance(data, dict):
                print(f"   Available keys: {list(data.keys())}")
            else:
                print(f"   Data value: {data}")
            continue

    # Sort by relative volume (lowest first - most consolidated)
    consolidation_candidates.sort(key=lambda x: x[1])

    return consolidation_candidates

def apply_rs_filter(consolidation_stocks: List[Tuple], min_rs_rating: int = 80) -> List[Tuple]:
    """
    Apply RS (Relative Strength) rating filter to consolidation candidates.

    Args:
        consolidation_stocks: List of consolidation candidate tuples
        min_rs_rating: Minimum RS rating threshold (default 80)

    Returns:
        List of tuples with RS rating added: (symbol, relative_volume, price, pct_change, volume, avg_volume, rs_rating)
    """
    if not consolidation_stocks:
        return []

    print(f"🎯 Applying RS rating filter (>= {min_rs_rating})...")

    # Load RS ratings from the rs_stocks.csv file
    rs_stocks_path = os.path.join("src", "data", "rs_data", "rs_stocks.csv")

    try:
        rs_df = pd.read_csv(rs_stocks_path)
        # Use the Percentile column as the RS rating
        rs_dict = dict(zip(rs_df['Ticker'], rs_df['Percentile']))

        print(f"📊 Loaded RS ratings for {len(rs_dict)} stocks")

        # Add RS ratings to consolidation candidates and filter
        filtered_stocks = []
        rs_found_count = 0

        for stock_data in consolidation_stocks:
            symbol = stock_data[0]
            rs_rating = rs_dict.get(symbol, 0)  # Default to 0 if not found

            if rs_rating > 0:
                rs_found_count += 1

            # Add RS rating to the tuple
            enhanced_stock_data = stock_data + (rs_rating,)

            # Apply RS filter
            if rs_rating >= min_rs_rating:
                filtered_stocks.append(enhanced_stock_data)

        print(f"📈 RS ratings found for {rs_found_count}/{len(consolidation_stocks)} stocks")
        print(f"✅ Filtered from {len(consolidation_stocks)} to {len(filtered_stocks)} stocks with RS >= {min_rs_rating}")

        if filtered_stocks:
            print("🏆 Sample of filtered stocks with RS ratings:")
            for i, stock in enumerate(filtered_stocks[:5], 1):
                symbol, rel_vol, _, _, _, _, rs_rating = stock
                print(f"  {i}. {symbol}: RS={rs_rating}, Rel Vol={rel_vol:.3f}")

        return filtered_stocks

    except FileNotFoundError:
        print(f"❌ RS stocks file not found at {rs_stocks_path}. Skipping RS filter.")
        # Return original data with RS rating of 0 added
        return [stock_data + (0,) for stock_data in consolidation_stocks]
    except Exception as e:
        print(f"❌ Error loading RS ratings: {e}. Skipping RS filter.")
        # Return original data with RS rating of 0 added
        return [stock_data + (0,) for stock_data in consolidation_stocks]

def scan_for_consolidation_stocks(file_path: str = None, max_relative_volume: float = 0.4) -> List[Tuple]:
    """
    Main function to scan for stocks with very low relative volume.

    Args:
        file_path: Path to Excel file with stock symbols (uses env var if None)
        max_relative_volume: Maximum relative volume ratio (default 0.4 = 40% of average)

    Returns:
        List of consolidation candidate stocks
    """
    if file_path is None:
        file_path = os.getenv('FIDELITY_FILE')

    if not file_path:
        raise ValueError("No file path provided and FIDELITY_FILE environment variable not set")

    print(f"🔍 Scanning for consolidation stocks from: {file_path}")
    print(f"📊 Max relative volume threshold: {max_relative_volume} ({max_relative_volume*100:.0f}% of 10-day average)")
    print(f"⏰ Scan time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 70)

    # Get stock list from Excel
    symbol_list = get_csv_stock_list_from_excel(file_path)
    print(f"📈 Total symbols to analyze: {len(symbol_list)}")

    # Process in chunks
    max_symbols_per_request = 50
    symbol_chunks = chunk_list(symbol_list, max_symbols_per_request)

    all_consolidation_stocks = []

    for i, symbols in enumerate(symbol_chunks, 1):
        print(f"Processing chunk {i}/{len(symbol_chunks)} ({len(symbols)} symbols)...", end=" ")

        symbols_string = ','.join(symbols)
        url = "https://digital.fidelity.com/prgw/digital/research/api/quote"

        payload = json.dumps({
            "symbol": symbols_string
        })

        headers = get_fidelity_headers_v2()

        try:
            response = requests.post(url, headers=headers, data=payload)

            # Check HTTP status
            if response.status_code != 200:
                print(f"❌ HTTP Error {response.status_code}: {response.text[:200]}")
                continue

            # Parse JSON response
            try:
                response_data = response.json()
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"   Response text: {response.text[:200]}")
                continue

            # Debug: Print response structure for troubleshooting
            if i == 1:  # Only for first chunk to avoid spam
                print(f"\n🔍 Debug - Response keys: {list(response_data.keys())}")
                if 'quoteData' in response_data and response_data['quoteData']:
                    first_item = response_data['quoteData'][0]
                    print(f"🔍 Debug - First item type: {type(first_item)}")
                    if isinstance(first_item, dict):
                        print(f"🔍 Debug - First item keys: {list(first_item.keys())}")
                    else:
                        print(f"🔍 Debug - First item value: {first_item}")
                print()

            # Analyze for low volume stocks
            consolidation_stocks = analyze_low_volume_stocks(response_data, max_relative_volume)
            all_consolidation_stocks.extend(consolidation_stocks)

            print(f"✅ Found {len(consolidation_stocks)} candidates")

        except requests.RequestException as e:
            print(f"❌ Request failed: {e}")
            continue
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            print(f"   Symbols in chunk: {symbols}")
            continue

    # Sort all results by relative volume (lowest first)
    all_consolidation_stocks.sort(key=lambda x: x[1])

    print(f"\n🎯 Found {len(all_consolidation_stocks)} total consolidation candidates")

    return all_consolidation_stocks

def display_consolidation_results(consolidation_stocks: List[Tuple], top_n: int = 20):
    """Display the consolidation analysis results in a formatted table."""

    if not consolidation_stocks:
        print("❌ No consolidation candidates found with the specified criteria.")
        return

    # Check if RS rating is included (7 elements vs 6)
    has_rs_rating = len(consolidation_stocks[0]) == 7 if consolidation_stocks else False

    print(f"\n🏆 Top {min(top_n, len(consolidation_stocks))} Consolidation Candidates (Lowest Relative Volume):")

    if has_rs_rating:
        print("=" * 105)
        print(f"{'Symbol':<8} {'Rel Vol':<8} {'Price':<10} {'% Change':<10} {'Volume':<12} {'Avg Vol':<12} {'RS':<5}")
        print("-" * 105)
    else:
        print("=" * 90)
        print(f"{'Symbol':<8} {'Rel Vol':<8} {'Price':<10} {'% Change':<10} {'Volume':<12} {'Avg Vol':<12}")
        print("-" * 90)

    for stock in consolidation_stocks[:top_n]:
        if has_rs_rating:
            symbol, rel_vol, price, pct_change, volume, avg_volume, rs_rating = stock
            # Format volume numbers for readability
            volume_str = f"{volume:,.0f}" if isinstance(volume, (int, float)) else str(volume)
            avg_volume_str = f"{avg_volume:,.0f}" if isinstance(avg_volume, (int, float)) else str(avg_volume)
            rs_str = f"{rs_rating:.0f}" if isinstance(rs_rating, (int, float)) else str(rs_rating)

            print(f"{symbol:<8} {rel_vol:<8.3f} {price:<10} {pct_change:<10} {volume_str:<12} {avg_volume_str:<12} {rs_str:<5}")
        else:
            symbol, rel_vol, price, pct_change, volume, avg_volume = stock
            # Format volume numbers for readability
            volume_str = f"{volume:,.0f}" if isinstance(volume, (int, float)) else str(volume)
            avg_volume_str = f"{avg_volume:,.0f}" if isinstance(avg_volume, (int, float)) else str(avg_volume)

            print(f"{symbol:<8} {rel_vol:<8.3f} {price:<10} {pct_change:<10} {volume_str:<12} {avg_volume_str:<12}")

    # Summary statistics
    if len(consolidation_stocks) > 0:
        avg_rel_vol = sum(stock[1] for stock in consolidation_stocks) / len(consolidation_stocks)
        min_rel_vol = min(stock[1] for stock in consolidation_stocks)
        max_rel_vol = max(stock[1] for stock in consolidation_stocks)

        print("\n📊 Summary Statistics:")
        print(f"   • Average relative volume: {avg_rel_vol:.3f}")
        print(f"   • Minimum relative volume: {min_rel_vol:.3f}")
        print(f"   • Maximum relative volume: {max_rel_vol:.3f}")

        if has_rs_rating:
            rs_ratings = [stock[6] for stock in consolidation_stocks if isinstance(stock[6], (int, float)) and stock[6] > 0]
            if rs_ratings:
                avg_rs = sum(rs_ratings) / len(rs_ratings)
                min_rs = min(rs_ratings)
                max_rs = max(rs_ratings)
                print(f"   • Average RS rating: {avg_rs:.1f}")
                print(f"   • Minimum RS rating: {min_rs:.0f}")
                print(f"   • Maximum RS rating: {max_rs:.0f}")
                print(f"   • Stocks with RS data: {len(rs_ratings)}/{len(consolidation_stocks)}")

def export_to_csv(consolidation_stocks: List[Tuple], filename: str = None):
    """Export consolidation results to CSV file."""

    if not consolidation_stocks:
        print("No data to export.")
        return

    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"consolidation_candidates_{timestamp}.csv"

    # Check if RS rating is included (7 elements vs 6)
    has_rs_rating = len(consolidation_stocks[0]) == 7 if consolidation_stocks else False

    # Create DataFrame with appropriate columns
    if has_rs_rating:
        columns = ['Symbol', 'Relative_Volume', 'Price', 'Percent_Change', 'Volume', 'Average_Volume', 'RS_Rating']
    else:
        columns = ['Symbol', 'Relative_Volume', 'Price', 'Percent_Change', 'Volume', 'Average_Volume']

    df = pd.DataFrame(consolidation_stocks, columns=columns)

    # Save to CSV
    output_path = os.path.join("src", "data", "screener", filename)
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    df.to_csv(output_path, index=False)

    print(f"💾 Results exported to: {output_path}")

def get_screener_files(directory: str = None) -> List[str]:
    """Get all Excel files from the screener directory."""
    if directory is None:
        directory = os.path.join("src/data", "screener")

    if not os.path.exists(directory):
        raise ValueError(f"Directory not found: {directory}")

    # Get all Excel files in the directory
    excel_files = glob.glob(os.path.join(directory, "filtered_sorted*.xlsx"))
    excel_files.extend(glob.glob(os.path.join(directory, "filtered_sorted*.xls")))

    if not excel_files:
        raise ValueError(f"No Excel files found in directory: {directory}")

    # Sort files by modification time (newest first)
    excel_files.sort(key=os.path.getmtime, reverse=True)

    return excel_files

def scan_multiple_files(directory: str = None, max_relative_volume: float = 0.4, rs_filter: int = 0) -> Dict[str, List[Tuple]]:
    """
    Scan for consolidation stocks across multiple Excel files.

    Args:
        directory: Directory containing Excel files (default: data/screener)
        max_relative_volume: Maximum relative volume ratio
        rs_filter: Minimum RS rating filter (0 to disable)

    Returns:
        Dictionary mapping file names to consolidation candidates
    """
    if directory is None:
        directory = os.path.join("data", "screener")

    excel_files = get_screener_files(directory)

    print(f"🗂️  Found {len(excel_files)} Excel files in {directory}")
    print("=" * 80)

    all_results = {}
    all_symbols_processed = set()
    combined_results = []

    for i, file_path in enumerate(excel_files, 1):
        file_name = os.path.basename(file_path)
        print(f"\n📁 Processing file {i}/{len(excel_files)}: {file_name}")

        try:
            # Scan this file
            consolidation_candidates = scan_for_consolidation_stocks(file_path, max_relative_volume)
            all_results[file_name] = consolidation_candidates

            # Add unique symbols to combined results (keep the best relative volume for each symbol)
            for candidate in consolidation_candidates:
                symbol = candidate[0]
                if symbol not in all_symbols_processed:
                    combined_results.append(candidate)
                    all_symbols_processed.add(symbol)
                else:
                    # If symbol already exists, keep the one with lower relative volume (better consolidation)
                    existing_idx = next(i for i, x in enumerate(combined_results) if x[0] == symbol)
                    if candidate[1] < combined_results[existing_idx][1]:  # Lower relative volume is better
                        combined_results[existing_idx] = candidate

            print(f"✅ Found {len(consolidation_candidates)} candidates from {file_name}")

        except Exception as e:
            print(f"❌ Error processing {file_name}: {e}")
            all_results[file_name] = []

    # Sort combined results by relative volume (lowest first - best consolidation)
    combined_results.sort(key=lambda x: x[1])

    print(f"\n🎯 Combined Results: {len(combined_results)} unique consolidation candidates")

    # Apply RS filter if specified
    if rs_filter > 0:
        print("=" * 80)
        combined_results = apply_rs_filter(combined_results, rs_filter)

    print("=" * 80)

    return all_results, combined_results

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Low Volume Consolidation Scanner - Identifies stocks with very low relative volume for consolidation analysis',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python low_volume_consolidation_scanner.py                    # Use default 0.3 (30%) with RS >= 80
  python low_volume_consolidation_scanner.py --threshold 0.4    # Use 40% threshold with RS >= 80
  python low_volume_consolidation_scanner.py -t 0.5             # Use 50% threshold with RS >= 80
  python low_volume_consolidation_scanner.py --rs-filter 90     # Use RS >= 90 filter
  python low_volume_consolidation_scanner.py --rs-filter 0      # Disable RS filtering
  python low_volume_consolidation_scanner.py --single-file      # Scan only single file from env var
        """
    )

    parser.add_argument(
        '-t', '--threshold',
        type=float,
        default=0.3,
        help='Maximum relative volume threshold (default: 0.3 = 30%% of 10-day average)'
    )

    parser.add_argument(
        '--single-file',
        action='store_true',
        help='Scan only single file from FIDELITY_FILE env var instead of all files in directory'
    )

    parser.add_argument(
        '--top-results',
        type=int,
        default=25,
        help='Number of top results to display in detailed table (default: 25, use 0 for all)'
    )

    parser.add_argument(
        '--no-export',
        action='store_true',
        help='Skip CSV export'
    )

    parser.add_argument(
        '--rs-filter',
        type=int,
        default=80,
        help='Minimum RS rating filter (1-99, default: 80). Use 0 to disable RS filtering.'
    )

    return parser.parse_args()

if __name__ == "__main__":
    # Parse command line arguments
    args = parse_arguments()

    # Configuration from arguments
    MAX_RELATIVE_VOLUME = args.threshold
    TOP_N_RESULTS = args.top_results if args.top_results > 0 else float('inf')
    EXPORT_TO_CSV = not args.no_export
    SCAN_MULTIPLE_FILES = not args.single_file
    RS_FILTER = args.rs_filter

    print(f"🔧 Configuration:")
    print(f"   • Max relative volume threshold: {MAX_RELATIVE_VOLUME} ({MAX_RELATIVE_VOLUME*100:.0f}% of 10-day average)")
    print(f"   • RS rating filter: {'Disabled' if RS_FILTER == 0 else f'>= {RS_FILTER}'}")
    print(f"   • Scan mode: {'Multi-file' if SCAN_MULTIPLE_FILES else 'Single-file'}")
    print(f"   • Export to CSV: {'Yes' if EXPORT_TO_CSV else 'No'}")
    print(f"   • Top results display: {'All' if TOP_N_RESULTS == float('inf') else int(TOP_N_RESULTS)}")
    print()

    try:
        if SCAN_MULTIPLE_FILES:
            print("🚀 Starting Multi-File Low Volume Consolidation Scanner")
            print("=" * 60)

            # Scan all files in the screener directory
            file_results, combined_results = scan_multiple_files(max_relative_volume=MAX_RELATIVE_VOLUME, rs_filter=RS_FILTER)

            # Display combined results
            display_count = len(combined_results) if TOP_N_RESULTS == float('inf') else min(int(TOP_N_RESULTS), len(combined_results))
            print(f"\n🏆 COMBINED RESULTS - {'All' if TOP_N_RESULTS == float('inf') else 'Top'} {display_count} Consolidation Candidates:")
            display_consolidation_results(combined_results, top_n=display_count)

            # Export combined results to CSV
            if EXPORT_TO_CSV and combined_results:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"combined_consolidation_candidates_{timestamp}.csv"
                export_to_csv(combined_results, filename)

            # Print symbols for easy copy-paste - ALL symbols, not just top N
            if combined_results:
                all_symbols = [stock[0] for stock in combined_results]  # Get ALL symbols
                print(f"\n📋 All {len(all_symbols)} symbols for copy-paste:")
                print(all_symbols)
                print("\n📋 Formatted in groups of 30:")
                symbol_chunks = [",".join(all_symbols[i:i+30]) for i in range(0, len(all_symbols), 30)]
                for chunk in symbol_chunks:
                    print(chunk)

            # Display summary by file
            print(f"\n📊 SUMMARY BY FILE:")
            print("-" * 50)
            for file_name, candidates in file_results.items():
                print(f"{file_name}: {len(candidates)} candidates")

        else:
            print("🚀 Starting Single-File Low Volume Consolidation Scanner")
            print("=" * 60)

            # Run the consolidation scan on single file
            consolidation_candidates = scan_for_consolidation_stocks(max_relative_volume=MAX_RELATIVE_VOLUME)

            # Apply RS filter if specified
            if RS_FILTER > 0:
                consolidation_candidates = apply_rs_filter(consolidation_candidates, RS_FILTER)

            # Display results
            display_count = len(consolidation_candidates) if TOP_N_RESULTS == float('inf') else min(int(TOP_N_RESULTS), len(consolidation_candidates))
            display_consolidation_results(consolidation_candidates, top_n=display_count)

            # Export to CSV if requested
            if EXPORT_TO_CSV and consolidation_candidates:
                export_to_csv(consolidation_candidates)

            # Print symbols for easy copy-paste - ALL symbols
            if consolidation_candidates:
                all_symbols = [stock[0] for stock in consolidation_candidates]  # Get ALL symbols
                print(f"\n📋 All {len(all_symbols)} symbols for copy-paste:")
                print(all_symbols)
                print("\n📋 Formatted in groups of 30:")
                symbol_chunks = [",".join(all_symbols[i:i+30]) for i in range(0, len(all_symbols), 30)]
                for chunk in symbol_chunks:
                    print(chunk)

    except Exception as e:
        print(f"❌ Error during scan: {e}")
        import traceback
        traceback.print_exc()
