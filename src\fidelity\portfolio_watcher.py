import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

import os
import time
import requests
import json
from dotenv import load_dotenv
from src.fidelity.common.api_headers import get_positions_headers, get_csrf_token

# Load environment variables
load_dotenv()

def get_portfolio_positions():
    """Fetches portfolio positions from Fidelity."""
    cookie = os.getenv('FIDELITY_POSITIONS_COOKIE')
    if not cookie:
        print("Error: FIDELITY_POSITIONS_COOKIE not found in .env file.")
        return

    headers = get_positions_headers(cookie)

    # The JSON payload from your curl command
    payload = {
        "accts": ["Z04545805"],
        "amc": "H4sIAAAAAAAAA+VVXU/iQBT9K2Sea1K0rS5vFXQhMUigMfsRH4bObblhmGnmAxcJ/90Z6Va24IuJ8WHf2nPP/cg5p+mW0DyXVhhNer+3RFYGpUhLBbACYUiP/CSBp5i+AuprA2qA9LrxZZJE8XkYBn8HjJhj/wqjOIqvwth1CcyXY7pybDISDNfILOWds052P3DVHN30gnIN7pnqRbqf0mAcxRLYtZJLULSEdjmXnEMJI7EGbfypmbLatFlGUYaiTDmXT+DuM8o6dH40dI9TISyaTXsIgwIFsL4URuHcvgrUooCWVRtDPQWDqtbxgDjhVDRAoeTq1nL+gPDUgApK1O52vyrbVE7BuBuQSipTSI4yZWvUUm1moNaYg25vrg15X3vv534uaQSu4Zmdn6gUhWwvKRTS8mizNQup8BmYq7zBGg0cxcO74FljuyI94SRoDJgoyezJC11Qbid3N7x0+Ni9uwjQfDOUVsNQcmeRNtdUo+5L5ltT4m2o5XoNSHaX/nOZoX8yH2xvUt2UpT9uBmQXbE/FPkm+hWFy1Y79edSNLi/CuBX76X027Iym6f8X9/2GL0j7geRflvLDNHwk5Z+R6nTwkH0nu8eALKieTWYNt3oW7pPy7f4P8Lh7AWqzHbYRBgAA",
        "externalCustId": "dd5fdff1050aed60b32000a1eb0000aa33",
        "isRefresh": False,
        "settings": {
            "groupBy": "0",
            "filter": "",
            "gbsMsgSeenDates": "Jul-12-2025,Aug-13-2025",
            "isUnadjusted": False,
            "isShowForeignStocks": False,
            "showBasketMsg": True,
            "showTodaysGLResetMsg": True,
            "showGbsMsg": True,
            "isPriorDayGL": False,
            "additionalFilter": ""
        },
        "isBasketEnable": False,
        "isGroupBySecurityEnable": False,
        "toggles": {
            "ap126216_PWE_Symbol_Total_Row": True,
            "ap126216_PWE_Baskets_Row_Message": False,
            "ap126216_PWE_TodaysGL_Reset_Settings_Message": False,
            "ap126216_PWE_GbS_Account_Row_Msg": True,
            "ap126216_PWE_PriorDayGL_Setting": True,
            "ap126216_PWE_Additional_Filters": True,
            "ap126216_PWE_Alt_Inv_Cost_Basis_Info": False,
            "ap126216_PWE_Custom_Columns": False
        }
    }

    try:
        response = requests.post(
            'https://digital.fidelity.com/ftgw/digital/positions/poswebex/api/positions',
            headers=headers,
            json=payload
        )
        response.raise_for_status()
        positions = response.json()

        print("--- Portfolio Positions ---")
        for item in positions.get('rowData', []):
            if item.get('rowType') == 'POSITION':
                symbol = item.get('sym', {}).get('name', 'N/A')
                quantity = item.get('qty', {}).get('disp', 'N/A')
                current_value = item.get('curVal', {}).get('disp', 'N/A')
                today_gl = item.get('todGLStk', {}).get('top', {}).get('disp', 'N/A')
                print(f"Symbol: {symbol}, Quantity: {quantity}, Value: {current_value}, Today's G/L: {today_gl}")
        print("---------------------------\n")

    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    while True:
        get_portfolio_positions()
        print("Waiting for 5 minutes before the next update...")
        time.sleep(300)
