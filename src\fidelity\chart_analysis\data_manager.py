import os
import datetime

DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'src', 'data')
LIKES_OUTPUT_PATH = os.path.join(DATA_DIR, 'liked_tickers.txt')
LIKES_CSV_PATH = os.path.join(DATA_DIR, 'liked_tickers_history.csv')
SCREENER_DIR = os.path.join(DATA_DIR, 'screener')

def _ensure_data_dirs():
    os.makedirs(DATA_DIR, exist_ok=True)
    os.makedirs(SCREENER_DIR, exist_ok=True)

def _append_like(ticker: str):
    _ensure_data_dirs()
    existing = []
    if os.path.exists(LIKES_OUTPUT_PATH):
        with open(LIKES_OUTPUT_PATH, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if content:
                existing = [x.strip().upper() for x in content.split(',') if x.strip()]
    if ticker not in existing:
        existing.append(ticker)
    with open(LIKES_OUTPUT_PATH, 'w', encoding='utf-8') as f:
        f.write(','.join(existing))

def _remove_like(ticker: str):
    _ensure_data_dirs()
    existing = []
    if os.path.exists(LIKES_OUTPUT_PATH):
        with open(LIKES_OUTPUT_PATH, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if content:
                existing = [x.strip().upper() for x in content.split(',') if x.strip()]
    if ticker in existing:
        existing.remove(ticker)
    with open(LIKES_OUTPUT_PATH, 'w', encoding='utf-8') as f:
        f.write(','.join(existing))

def _read_likes():
    if not os.path.exists(LIKES_OUTPUT_PATH):
        return []
    content = open(LIKES_OUTPUT_PATH, 'r', encoding='utf-8').read().strip()
    if not content:
        return []
    return [x.strip().upper() for x in content.split(',') if x.strip()]

def _get_snapshot_date_yyyy_mm_dd():
    """Return YYYY-MM-DD from latest filtered_sorted_screener_*.xlsx, fallback to today."""
    try:
        files = []
        if os.path.isdir(SCREENER_DIR):
            for name in os.listdir(SCREENER_DIR):
                if name.startswith('filtered_sorted_screener_') and name.endswith('.xlsx'):
                    date_part = name[len('filtered_sorted_screener_'):-len('.xlsx')]
                    files.append(date_part)
        valid_dates = []
        for d in files:
            try:
                datetime.datetime.strptime(d, '%Y-%m-%d')
                valid_dates.append(d)
            except Exception:
                pass
        if valid_dates:
            return max(valid_dates)
    except Exception:
        pass
    return datetime.datetime.now().strftime('%Y-%m-%d')
