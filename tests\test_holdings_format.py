import sys
import os

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from util.scrapper import etfHoldingsETFDBdotCOM, fundHoldingsYahoo
from tabulate import tabulate

def test_etf_holdings():
    """Test the etfHoldingsETFDBdotCOM function with AMD ticker."""
    ticker = 'AMD'
    print(f"Testing etfHoldingsETFDBdotCOM with ticker: {ticker}")

    # Call the function
    try:
        result = etfHoldingsETFDBdotCOM(ticker)
        
        # Print the column names to verify they're fixed
        print(f"Column names: {result.columns.tolist()}")
        
        # Print the result
        if result is not None and not result.empty:
            print(f"Success! Found {len(result)} rows of ETF holdings data.")
            print("\nETF Holdings:")
            print(tabulate(result.head(3), headers='keys', tablefmt='fancy_grid', showindex=False))
        else:
            print("No data returned or empty DataFrame.")
    except Exception as e:
        print(f"Error testing ETF holdings: {e}")

def test_fund_holdings():
    """Test the fundHoldingsYahoo function with AMD ticker."""
    ticker = 'AMD'
    print(f"\nTesting fundHoldingsYahoo with ticker: {ticker}")

    # Call the function
    try:
        result = fundHoldingsYahoo(ticker)
        
        # Print the column names to verify they're fixed
        print(f"Column names: {result.columns.tolist()}")
        
        # Print the result
        if result is not None and not result.empty:
            print(f"Success! Found {len(result)} rows of fund holdings data.")
            print("\nFund Holdings:")
            print(tabulate(result.head(3), headers='keys', tablefmt='fancy_grid', showindex=False))
        else:
            print("No data returned or empty DataFrame.")
    except Exception as e:
        print(f"Error testing fund holdings: {e}")

if __name__ == "__main__":
    test_etf_holdings()
    test_fund_holdings()
