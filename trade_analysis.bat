@echo off
REM Activate the Python virtual environment
call C:\Users\<USER>\Desktop\pythonicfin\pythonicfin_env_py311\Scripts\activate.bat

REM Change to the tradeanalysis directory
cd /d C:\Users\<USER>\Desktop\pythonicfin\src\tradeanalysis

REM Run buy_sell_to_trades.py
python buy_sell_to_trades.py

REM Run performance_analyzer.py
python performance_analyzer.py

REM Run equitycurve.py
python equitycurve.py

REM Keep the window open so you can see the results
pause
