import datetime
import os
import aiohttp
from stockdata.data_source import getStockDataV3

async def process_ticker(session: aiohttp.ClientSession, row, doc, save_doc,daily_id, wkly_id):
    xyz = row['Symbol']
    df = await getStockDataV3(session, xyz)

    if (df is not None
        and df.size > 0
        and df['slope_50'][0] > 0
        and df['slope_200'][0] > 0
        and df['UDRatio'][0] > 0.75
        and df['150'][0] > df['200'][0]
        and df['50'][0] > df['150'][0]
        and df['50'][0] > df['200'][0]
        and df['$VolumeM'][0] >= 25
        and ('RS_Rating' in df.columns and df['RS_Rating'][0] > 77)
    ):
        if save_doc:
            # This part is now handled by docx_generator
            pass

    else:
        return xyz, determine_exclusion_reasons(df) , df
    return None, None , df

def determine_exclusion_reasons(df):
    reasons = []
    if df is None:
       reasons.append("DataFrame is None")
       return
    if df.size == 0:
        reasons.append("DataFrame is empty")
    if df['slope_50'].iloc[0] <= 0:
        reasons.append("slope_50 not > 0")
    if df['slope_200'].iloc[0] <= 0:
        reasons.append("slope_200 not > 0")
    if df['UDRatio'].iloc[0] <= 0.75:
        reasons.append("UDRatio not > 0.75")
    if not (df['150'].iloc[0] > df['200'].iloc[0]):
        reasons.append("150 not > 200")
    if not (df['50'].iloc[0] > df['150'].iloc[0]):
        reasons.append("50 not > 150")
    if not (df['50'].iloc[0] > df['200'].iloc[0]):
        reasons.append("50 not > 200")
    if df['$VolumeM'].iloc[0] < 25:
        reasons.append("$VolumeM not > 25")
    if 'RS_Rating' in df.columns and df['RS_Rating'].iloc[0] <= 77:
        reasons.append("RS_Rating not > 77")
    elif 'RS_Rating' not in df.columns:
        reasons.append("RS_Rating not available")
    return ', '.join(reasons)

def save_filtered_screener(screenerdf, excluded_tickers):
    excluded_symbols = [ticker for ticker, _ in excluded_tickers]
    df = screenerdf.loc[~screenerdf['Symbol'].isin(excluded_symbols)].copy()

    df['Sector_Count'] = df.groupby('Sector')['Percentile'].transform('size')
    df['Industry_Count'] = df.groupby(['Sector', 'Industry'])['Percentile'].transform('size')
    df['SubIndustry_Count'] = df.groupby(['Sector', 'Industry', 'Sub-Industry'])['Percentile'].transform('size')
    df = df.sort_values(by=['Sector_Count', 'Industry_Count', 'SubIndustry_Count'], ascending=[False, False, False])
    df['Rank'] = df.groupby(['Sector', 'Industry', 'Sub-Industry'])['Percentile'].rank(ascending=False)
    df = df.sort_values(by=['Sector_Count', 'Industry_Count', 'SubIndustry_Count', 'Rank'], ascending=[False, False, False, True])
    df.drop(columns=['Sector_Count', 'Industry_Count', 'SubIndustry_Count'], inplace=True)

    screener_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'src/data', 'screener')
    os.makedirs(screener_dir, exist_ok=True)
    current_date = datetime.datetime.now().strftime("%Y-%m-%d")
    file_name = f"filtered_sorted_screener_{current_date}.xlsx"
    file_path = os.path.join(screener_dir, file_name)
    df.to_excel(file_path)
    print(f"Saved filtered screener to {file_path}")
    return df

async def filter_stocks(session: aiohttp.ClientSession, screenerdf):
    """
    Filter stocks based on technical criteria and return filtered dataframe and excluded tickers
    """
    excluded_tickers = []

    for _, row in screenerdf.iterrows():
        xyz = row['Symbol']
        df = await getStockDataV3(session, xyz)

        if df is not None and df.size > 0:
            # evaluate trend block
            trend_ok = (
                df['slope_50'].iloc[0] > 0
                and df['slope_200'].iloc[0] > 0
                and df['UDRatio'].iloc[0] > 0.75
                and df['150'].iloc[0] > df['200'].iloc[0]
                and df['50'].iloc[0] > df['150'].iloc[0]
                and df['50'].iloc[0] > df['200'].iloc[0]
            )


            # MOMO boolean shortcut
            momo_ok = bool(row['MOMO'])

            if (
                df['$VolumeM'].iloc[0] >= 25
                and ('RS_Rating' in df.columns and df['RS_Rating'].iloc[0] > 77)
                and (trend_ok or momo_ok)
            ):
                # print only if inclusion happened via MOMO (trend failed)
                if momo_ok and not trend_ok:
                    print(f"{xyz} INCLUDED via MOMO")

                if not df.empty:
                    if 'TTM' in df.columns:
                        try:
                            screenerdf.at[row.name, 'TTM'] = df['TTM'].iloc[0]
                        except (KeyError, IndexError):
                            print(f"Issue adding TTM for {xyz}")
                    if 'RS_Rating' in df.columns:
                        try:
                            screenerdf.at[row.name, 'Percentile'] = df['RS_Rating'].iloc[0]
                        except (KeyError, IndexError):
                            print(f"Issue adding RS_Rating for {xyz}")
            else:
                reason = determine_exclusion_reasons(df)
                excluded_tickers.append((xyz, reason))
        else:
            reason = determine_exclusion_reasons(df)
            excluded_tickers.append((xyz, reason))

    return screenerdf, excluded_tickers
